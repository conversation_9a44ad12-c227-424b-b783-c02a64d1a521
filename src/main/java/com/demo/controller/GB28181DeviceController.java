package com.demo.controller;

import com.demo.entity.GB28181Device;
import com.demo.service.GB28181DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.demo.service.ZLMediaKitService;
import com.demo.service.GB28181SipServer;

/**
 * 设备管理控制器
 * 提供设备管理的REST API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/device")
public class GB28181DeviceController {
    
    @Autowired
    private GB28181DeviceService deviceService;
    
    @Autowired
    private ZLMediaKitService zlmService;

    @Autowired
    private GB28181SipServer sipServer;
    
    /**
     * 获取所有设备列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getDeviceList() {
        try {
            List<GB28181Device> devices = deviceService.getAllDevices();
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("msg", "success");
            result.put("data", devices);
            result.put("total", devices.size());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "获取设备列表失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取在线设备列表
     */
    @GetMapping("/online")
    public ResponseEntity<Map<String, Object>> getOnlineDevices() {
        try {
            List<GB28181Device> devices = deviceService.getOnlineDevices();
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("msg", "success");
            result.put("data", devices);
            result.put("total", devices.size());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取在线设备列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "获取在线设备列表失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 根据设备ID获取设备信息
     */
    @GetMapping("/{deviceId}")
    public ResponseEntity<Map<String, Object>> getDevice(@PathVariable String deviceId) {
        try {
            GB28181Device device = deviceService.getDevice(deviceId);

            Map<String, Object> result = new HashMap<>();
            if (device != null) {
                result.put("code", 0);
                result.put("msg", "success");
                result.put("data", device);
            } else {
                result.put("code", -1);
                result.put("msg", "设备不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备信息失败: {}", deviceId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "获取设备信息失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 邀请设备开始推流
     */
    @PostMapping("/{deviceId}/invite")
    public ResponseEntity<Map<String, Object>> inviteDevice(@PathVariable String deviceId) {
        try {
            GB28181Device device = deviceService.getDevice(deviceId);

            Map<String, Object> result = new HashMap<>();
            if (device != null && "online".equals(device.getStatus())) {
                // 调用SIP服务器主动邀请推流
                boolean success = sipServer.inviteDeviceStream(deviceId);

                if (success) {
                    result.put("code", 0);
                    result.put("msg", "邀请推流请求已发送");
                    result.put("deviceId", deviceId);
                } else {
                    result.put("code", -1);
                    result.put("msg", "邀请推流失败");
                }
            } else {
                result.put("code", -1);
                result.put("msg", device == null ? "设备不存在" : "设备离线");
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("邀请设备推流失败: {}", deviceId, e);

            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "邀请推流失败: " + e.getMessage());

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取设备播放地址
     */
    @GetMapping("/{deviceId}/play")
    public ResponseEntity<Map<String, Object>> getPlayUrl(@PathVariable String deviceId,
                                                         @RequestParam(defaultValue = "flv") String format) {
        try {
            GB28181Device device = deviceService.getDevice(deviceId);

            Map<String, Object> result = new HashMap<>();
            if (device != null) {
                
                if ("online".equals(device.getStatus())) {
                    // 检查流是否存在
                    boolean streamExists = zlmService.isStreamExists(deviceId);

                    // 如果流不存在，等待一下（被动模式下等待设备主动推流）
                    if (!streamExists) {
                        try {
                            log.info("流不存在，等待设备主动推流: {}", deviceId);
                            Thread.sleep(3000); // 等待3秒
                            streamExists = zlmService.isStreamExists(deviceId);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }

                    if (streamExists) {
                        String playUrl = zlmService.generatePlayUrl(deviceId, format);

                        Map<String, String> urls = new HashMap<>();
                        urls.put("flv", zlmService.generatePlayUrl(deviceId, "flv"));
                        urls.put("hls", zlmService.generatePlayUrl(deviceId, "hls"));
                        urls.put("rtmp", zlmService.generatePlayUrl(deviceId, "rtmp"));

                        result.put("code", 0);
                        result.put("msg", "success");
                        result.put("data", Map.of(
                            "deviceId", deviceId,
                            "playUrl", playUrl,
                            "urls", urls,
                            "status", "streaming"
                        ));
                    } else {
                        result.put("code", -1);
                        result.put("msg", "设备未推流，请稍后重试");
                    }
                } else {
                    result.put("code", -1);
                    result.put("msg", "设备离线");
                }
            } else {
                result.put("code", -1);
                result.put("msg", "设备不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取播放地址失败: {}", deviceId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "获取播放地址失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 设置设备离线
     */
    @PostMapping("/{deviceId}/offline")
    public ResponseEntity<Map<String, Object>> setDeviceOffline(@PathVariable String deviceId) {
        try {
            deviceService.setDeviceOffline(deviceId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("msg", "设备已设置为离线");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("设置设备离线失败: {}", deviceId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "设置设备离线失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取流媒体列表
     */
    @GetMapping("/streams")
    public ResponseEntity<String> getStreams() {
        try {
            String mediaList = zlmService.getMediaList();
            return ResponseEntity.ok(mediaList);
        } catch (Exception e) {
            log.error("获取流媒体列表失败", e);
            return ResponseEntity.ok("{\"code\": -1, \"msg\": \"获取流媒体列表失败\"}");
        }
    }
    
    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            List<GB28181Device> allDevices = deviceService.getAllDevices();
            List<GB28181Device> onlineDevices = deviceService.getOnlineDevices();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalDevices", allDevices.size());
            stats.put("onlineDevices", onlineDevices.size());
            stats.put("offlineDevices", allDevices.size() - onlineDevices.size());
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("msg", "success");
            result.put("data", stats);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", -1);
            result.put("msg", "获取统计信息失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
}
