package com.demo.service;

import com.demo.config.GB28181Properties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import javax.sip.*;
import javax.sip.address.AddressFactory;
import javax.sip.header.HeaderFactory;
import javax.sip.message.MessageFactory;
import javax.sip.message.Request;
import javax.sip.message.Response;
import javax.sip.header.*;
import javax.sip.address.Address;
import javax.sip.address.SipURI;
import com.demo.entity.GB28181Device;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * GB28181 SIP服务器
 * 负责处理GB28181协议的SIP信令
 */
@Slf4j
@Component  // 重新启用SIP服务器
public class GB28181SipServer implements SipListener {
    
    @Autowired
    private GB28181Properties properties;
    
    @Autowired
    private GB28181DeviceService deviceService;
    
    @Autowired
    private ZLMediaKitService zlmService;
    
    private SipStack sipStack;
    private SipProvider sipProvider;
    private SipProvider tcpSipProvider;
    private AddressFactory addressFactory;
    private HeaderFactory headerFactory;
    private MessageFactory messageFactory;

    // 正在邀请推流的设备集合，防止并发邀请
    private final Set<String> invitingDevices = ConcurrentHashMap.newKeySet();
    // 设备邀请冷却时间，避免短时间重复邀请（毫秒）
    private static final long INVITE_COOLDOWN_MS = 8000;
    // 记录上次邀请时间
    private final ConcurrentHashMap<String, Long> lastInviteAt = new ConcurrentHashMap<>();
    
    /**
     * 初始化SIP服务器
     */
    @PostConstruct
    public void init() {
        try {
            initSipStack();
            startSipServer();
            log.info("GB28181 SIP服务器启动成功，监听端口: {}", properties.getSip().getPort());
        } catch (Exception e) {
            log.error("GB28181 SIP服务器启动失败", e);
            // 不抛出异常，允许应用继续启动
            log.info("SIP服务器启动失败，应用将继续运行但GB28181功能不可用");
        }
    }
    
    /**
     * 销毁SIP服务器
     */
    @PreDestroy
    public void destroy() {
        try {
            if (sipProvider != null) {
                sipProvider.removeSipListener(this);
            }
            if (tcpSipProvider != null) {
                tcpSipProvider.removeSipListener(this);
            }
            if (sipStack != null) {
                sipStack.stop();
                log.info("GB28181 SIP服务器已停止");
            }
        } catch (Exception e) {
            log.error("停止SIP服务器失败", e);
        }
    }
    
    /**
     * 初始化SIP协议栈
     */
    private void initSipStack() throws Exception {
        // 创建SIP工厂
        SipFactory sipFactory = SipFactory.getInstance();
        sipFactory.setPathName("gov.nist");

        // 配置SIP协议栈属性
        Properties sipProperties = new Properties();
        sipProperties.setProperty("javax.sip.STACK_NAME", "GB28181-" + System.currentTimeMillis()); // 使用唯一名称
        sipProperties.setProperty("javax.sip.IP_ADDRESS", properties.getNetwork().getSipBindIp());
        sipProperties.setProperty("gov.nist.javax.sip.TRACE_LEVEL", "32");
        sipProperties.setProperty("gov.nist.javax.sip.SERVER_LOG", "sipserver.log");
        sipProperties.setProperty("gov.nist.javax.sip.DEBUG_LOG", "sipdebug.log");

        // 创建SIP协议栈
        sipStack = sipFactory.createSipStack(sipProperties);

        // 创建工厂
        headerFactory = sipFactory.createHeaderFactory();
        addressFactory = sipFactory.createAddressFactory();
        messageFactory = sipFactory.createMessageFactory();
    }
    
    /**
     * 启动SIP服务器
     */
    private void startSipServer() throws Exception {
        // 先清理可能存在的Provider和监听点
        try {
            // 清理现有的Provider（先移除监听器）
            @SuppressWarnings("unchecked")
            java.util.Iterator<SipProvider> providers = sipStack.getSipProviders();
            while (providers.hasNext()) {
                SipProvider provider = providers.next();
                try {
                    provider.removeSipListener(this);
                } catch (Exception e) {
                    log.debug("移除SipListener失败: {}", e.getMessage());
                }
                sipStack.deleteSipProvider(provider);
            }

            // 清理现有的监听点
            @SuppressWarnings("unchecked")
            java.util.Iterator<ListeningPoint> points = sipStack.getListeningPoints();
            while (points.hasNext()) {
                ListeningPoint point = points.next();
                sipStack.deleteListeningPoint(point);
            }
        } catch (Exception e) {
            log.info("清理SIP资源时出错: {}", e.getMessage());
        }

        // 创建UDP监听点
        ListeningPoint udpListeningPoint = sipStack.createListeningPoint(
            properties.getNetwork().getSipBindIp(),
            properties.getSip().getPort(),
            "UDP"
        );

        // 创建TCP监听点
        ListeningPoint tcpListeningPoint = sipStack.createListeningPoint(
            properties.getNetwork().getSipBindIp(),
            properties.getSip().getPort(),
            "TCP"
        );

        // 创建UDP SIP提供者
        sipProvider = sipStack.createSipProvider(udpListeningPoint);
        sipProvider.addSipListener(this);

        // 创建TCP SIP提供者
        tcpSipProvider = sipStack.createSipProvider(tcpListeningPoint);
        tcpSipProvider.addSipListener(this);

        // 启动协议栈
        sipStack.start();
    }
    
    @Override
    public void processRequest(RequestEvent requestEvent) {
        Request request = requestEvent.getRequest();
        String method = request.getMethod();
        
        log.info("收到SIP请求: {}", method);
        
        try {
            switch (method) {
                case Request.REGISTER:
                    handleRegister(requestEvent);
                    break;
                case Request.MESSAGE:
                    handleMessage(requestEvent);
                    break;
                case Request.INVITE:
                    handleInvite(requestEvent);
                    break;
                case Request.BYE:
                    handleBye(requestEvent);
                    break;
                default:
                    log.info("未处理的SIP方法: {}", method);
                    sendResponse(requestEvent, Response.METHOD_NOT_ALLOWED);
            }
        } catch (Exception e) {
            log.error("处理SIP请求失败: {}", method, e);
            try {
                sendResponse(requestEvent, Response.SERVER_INTERNAL_ERROR);
            } catch (Exception ex) {
                log.error("发送错误响应失败", ex);
            }
        }
    }
    
    /**
     * 处理设备注册请求
     */
    private void handleRegister(RequestEvent requestEvent) throws Exception {
        Request request = requestEvent.getRequest();
        
        // 提取设备ID
        String deviceId = extractDeviceId(request);
        if (deviceId == null) {
            log.info("无法提取设备ID");
            sendResponse(requestEvent, Response.BAD_REQUEST);
            return;
        }
        
        // 验证设备
        if (deviceService.validateDevice(deviceId, properties.getSip().getPassword())) {
            // 保存设备信息
            deviceService.saveDevice(deviceId, request);

            // 发送200 OK响应
            sendOkResponse(requestEvent);

            log.info("设备注册成功: {}", deviceId);

            // 禁用自动邀请推流，改为被动模式 - 等待设备主动推流
            // autoInviteDeviceStream(deviceId);
            log.info("设备注册完成，等待设备主动推流: {}", deviceId);
        } else {
            // 发送401 Unauthorized响应
            sendResponse(requestEvent, Response.UNAUTHORIZED);
            
            log.info("设备注册失败，认证不通过: {}", deviceId);
        }
    }
    
    /**
     * 处理消息请求（心跳等）
     */
    private void handleMessage(RequestEvent requestEvent) throws Exception {
        Request request = requestEvent.getRequest();
        String deviceId = extractDeviceId(request);
        
        if (deviceId != null) {
            // 更新设备心跳时间
            deviceService.updateKeepalive(deviceId);
            
            // 发送200 OK响应
            sendOkResponse(requestEvent);
            
            log.info("收到设备心跳: {}", deviceId);
        } else {
            sendResponse(requestEvent, Response.BAD_REQUEST);
        }
    }
    
    /**
     * 处理媒体邀请请求
     */
    private void handleInvite(RequestEvent requestEvent) throws Exception {
        Request request = requestEvent.getRequest();
        String deviceId = extractDeviceId(request);
        
        if (deviceId == null) {
            sendResponse(requestEvent, Response.BAD_REQUEST);
            return;
        }
        
        // 分配RTP端口
        int rtpPort = zlmService.allocateRtpPort();
        
        // 通知ZLMediaKit准备接收RTP流
        if (zlmService.prepareRtpReceive(deviceId, rtpPort)) {
            // 保存设备的RTP端口分配
            deviceService.allocateRtpPort(deviceId, rtpPort);
            
            // 创建SDP响应
            String sdp = createSdpResponse(rtpPort);
            
            // 发送200 OK响应
            sendOkResponseWithSdp(requestEvent, sdp);
            
            log.info("邀请设备推流成功: {}, RTP端口: {}", deviceId, rtpPort);
        } else {
            sendResponse(requestEvent, Response.SERVER_INTERNAL_ERROR);
            log.error("准备接收RTP流失败: {}", deviceId);
        }
    }
    
    /**
     * 处理BYE请求（结束会话）
     */
    private void handleBye(RequestEvent requestEvent) throws Exception {
        String deviceId = extractDeviceId(requestEvent.getRequest());
        
        if (deviceId != null) {
            // 可以在这里清理RTP资源
            log.info("设备结束会话: {}", deviceId);
        }
        
        sendOkResponse(requestEvent);
    }
    
    @Override
    public void processResponse(ResponseEvent responseEvent) {
        Response response = responseEvent.getResponse();
        ClientTransaction clientTransaction = responseEvent.getClientTransaction();

        log.info("收到SIP响应: 状态码={}, 方法={}",
                response.getStatusCode(),
                clientTransaction != null ? clientTransaction.getRequest().getMethod() : "未知");

        // 打印完整响应内容
        log.debug("完整SIP响应: \n{}", response.toString());

        // 处理INVITE响应
        if (clientTransaction != null && "INVITE".equals(clientTransaction.getRequest().getMethod())) {
            handleInviteResponse(response, clientTransaction);
        }
    }

    /**
     * 处理INVITE响应
     */
    private void handleInviteResponse(Response response, ClientTransaction clientTransaction) {
        try {
            int statusCode = response.getStatusCode();
            String deviceId = extractDeviceIdFromResponse(response);

            log.info("INVITE响应: 设备={}, 状态码={}, 原因={}",
                    deviceId, statusCode, response.getReasonPhrase());

            if (statusCode == 200) {
                log.info("设备{}接受INVITE，开始推流", deviceId);

                // 打印完整的200 OK响应，包括SDP
                log.info("摄像机200 OK完整响应:\n{}", response.toString());

                // 解析摄像机SDP仅用于日志，不再在设备端口再次openRtpServer
                Object content = response.getContent();
                String cameraSdp = content != null ? new String((byte[]) content) : "";
                log.info("解析摄像机SDP: {}", cameraSdp);

                // 发送ACK确认（使用TCP）
                Request ackRequest = clientTransaction.createAck();
                tcpSipProvider.sendRequest(ackRequest);
            } else if (statusCode >= 400) {
                log.warn("设备{}拒绝INVITE: {} {}", deviceId, statusCode, response.getReasonPhrase());
            } else if (statusCode >= 100 && statusCode < 200) {
                log.info("设备{}临时响应: {} {}", deviceId, statusCode, response.getReasonPhrase());
            }
        } catch (Exception e) {
            log.error("处理INVITE响应失败", e);
        }
    }

    /**
     * 从响应中提取设备ID
     */
    private String extractDeviceIdFromResponse(Response response) {
        try {
            FromHeader fromHeader = (FromHeader) response.getHeader(FromHeader.NAME);
            if (fromHeader != null) {
                Address address = fromHeader.getAddress();
                if (address.getURI() instanceof SipURI) {
                    return ((SipURI) address.getURI()).getUser();
                }
            }
        } catch (Exception e) {
            log.debug("提取设备ID失败", e);
        }
        return "未知设备";
    }
    
    @Override
    public void processTimeout(TimeoutEvent timeoutEvent) {
        log.info("SIP请求超时");
    }
    
    @Override
    public void processIOException(IOExceptionEvent exceptionEvent) {
        log.error("SIP IO异常: {}", exceptionEvent.toString());
    }
    
    @Override
    public void processTransactionTerminated(TransactionTerminatedEvent transactionTerminatedEvent) {
        log.info("SIP事务终止");
    }
    
    @Override
    public void processDialogTerminated(DialogTerminatedEvent dialogTerminatedEvent) {
        log.info("SIP对话终止");
    }

    /**
     * 从SIP请求中提取设备ID
     */
    private String extractDeviceId(Request request) {
        try {
            FromHeader fromHeader = (FromHeader) request.getHeader(FromHeader.NAME);
            if (fromHeader != null) {
                Address fromAddress = fromHeader.getAddress();
                if (fromAddress.getURI().isSipURI()) {
                    SipURI sipURI = (SipURI) fromAddress.getURI();
                    String user = sipURI.getUser();

                    // 验证设备ID格式（20位数字）
                    if (user != null && user.matches("\\d{20}")) {
                        return user;
                    }
                }
            }
        } catch (Exception e) {
            log.error("提取设备ID失败", e);
        }
        return null;
    }

    /**
     * 发送OK响应
     */
    private void sendOkResponse(RequestEvent requestEvent) throws Exception {
        Request request = requestEvent.getRequest();
        Response response = messageFactory.createResponse(Response.OK, request);

        // 添加Date头
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        DateHeader dateHeader = headerFactory.createDateHeader(calendar);
        response.addHeader(dateHeader);

        // 如果是REGISTER请求，添加Expires头
        if (Request.REGISTER.equals(request.getMethod())) {
            ExpiresHeader expiresHeader = headerFactory.createExpiresHeader(properties.getSip().getRegisterInterval());
            response.addHeader(expiresHeader);
        }

        // 添加GB28181版本头
        response.addHeader(headerFactory.createHeader("X-GB-Ver", "2.0"));

        // 发送响应
        ServerTransaction serverTransaction = requestEvent.getServerTransaction();
        if (serverTransaction == null) {
            serverTransaction = sipProvider.getNewServerTransaction(request);
        }
        serverTransaction.sendResponse(response);
    }

    /**
     * 发送带SDP的OK响应
     */
    private void sendOkResponseWithSdp(RequestEvent requestEvent, String sdp) throws Exception {
        Response response = messageFactory.createResponse(Response.OK, requestEvent.getRequest());

        // 添加Content-Type头
        ContentTypeHeader contentTypeHeader = headerFactory.createContentTypeHeader("application", "sdp");
        response.setContent(sdp, contentTypeHeader);

        // 发送响应
        ServerTransaction serverTransaction = requestEvent.getServerTransaction();
        if (serverTransaction == null) {
            serverTransaction = sipProvider.getNewServerTransaction(requestEvent.getRequest());
        }
        serverTransaction.sendResponse(response);
    }

    /**
     * 发送响应
     */
    private void sendResponse(RequestEvent requestEvent, int statusCode) throws Exception {
        Response response = messageFactory.createResponse(statusCode, requestEvent.getRequest());

        ServerTransaction serverTransaction = requestEvent.getServerTransaction();
        if (serverTransaction == null) {
            serverTransaction = sipProvider.getNewServerTransaction(requestEvent.getRequest());
        }
        serverTransaction.sendResponse(response);
    }

    /**
     * 创建SDP响应内容
     */
    private String createSdpResponse(int rtpPort) {
        StringBuilder sdp = new StringBuilder();
        sdp.append("v=0\r\n");
        sdp.append("o=- 0 0 IN IP4 ").append(properties.getNetwork().getMediaIp()).append("\r\n");
        sdp.append("s=Play\r\n");
        sdp.append("c=IN IP4 ").append(properties.getNetwork().getMediaIp()).append("\r\n");
        sdp.append("t=0 0\r\n");
        sdp.append("m=video ").append(rtpPort).append(" TCP/RTP/AVP 96\r\n");
        sdp.append("a=rtpmap:96 PS/90000\r\n");
        sdp.append("a=sendonly\r\n");

        return sdp.toString();
    }

    /**
     * 自动邀请设备推流（注册后自动调用）
     *
     * @param deviceId 设备ID
     */
    private void autoInviteDeviceStream(String deviceId) {
        try {
            // 等待2秒，确保设备注册完成
            Thread.sleep(2000);

            // 邀请冷却：同一设备在冷却时间内不重复邀请
            Long last = lastInviteAt.get(deviceId);
            long now = System.currentTimeMillis();
            if (last != null && now - last < INVITE_COOLDOWN_MS) {
                log.info("设备{}处于邀请冷却期({}ms)，跳过本次自动邀请", deviceId, INVITE_COOLDOWN_MS);
                return;
            }
            lastInviteAt.put(deviceId, now);

            log.info("自动邀请设备推流: {}", deviceId);
            boolean success = inviteDeviceStream(deviceId);

            if (success) {
                log.info("自动邀请推流成功: {}", deviceId);
            } else {
                log.warn("自动邀请推流失败: {}", deviceId);
            }
        } catch (Exception e) {
            log.error("自动邀请推流异常: {}", deviceId, e);
        }
    }

    /**
     * 主动邀请设备推流
     *
     * @param deviceId 设备ID
     * @return 是否成功发送邀请
     */
    public boolean inviteDeviceStream(String deviceId) {
        // 检查是否已在邀请中，防止并发邀请
        if (!invitingDevices.add(deviceId)) {
            log.info("设备{}正在邀请中，跳过", deviceId);
            return false;
        }

        try {
            GB28181Device device = deviceService.getDevice(deviceId);
            if (device == null || !"online".equals(device.getStatus())) {
                log.warn("设备不在线，无法邀请推流: {}", deviceId);
                return false;
            }

            // 检查流是否已存在，避免重复邀请
            if (zlmService.isStreamExists(deviceId)) {
                log.info("设备{}已在推流，跳过邀请", deviceId);
                return true;
            }

            // 获取或分配RTP端口（复用已有端口）
            int rtpPort = deviceService.getOrAllocateRtpPort(deviceId);

            // 检查流是否已存在，避免重复创建
            if (zlmService.isStreamExists(deviceId)) {
                log.info("设备{}的流已存在，跳过RTP接收器创建", deviceId);
            } else {
                // 通知ZLMediaKit准备接收RTP流
                if (!zlmService.prepareRtpReceive(deviceId, rtpPort)) {
                    log.error("ZLMediaKit准备接收RTP失败: {}", deviceId);
                    return false;
                }
            }

            // 创建INVITE请求
            Request inviteRequest = createInviteRequest(deviceId, device.getIpAddress(), device.getPort(), rtpPort, device.getIpAddress());

            // 发送INVITE请求（使用TCP）
            ClientTransaction clientTransaction = tcpSipProvider.getNewClientTransaction(inviteRequest);
            clientTransaction.sendRequest();

            // 保存设备的RTP端口分配
            deviceService.allocateRtpPort(deviceId, rtpPort);

            log.info("主动邀请设备推流: {}, RTP端口: {}", deviceId, rtpPort);
            return true;

        } catch (Exception e) {
            log.error("主动邀请设备推流失败: {}", deviceId, e);
            return false;
        } finally {
            // 无论成功失败，都要从邀请集合中移除
            invitingDevices.remove(deviceId);
        }
    }

    /**
     * 创建INVITE请求
     */
    private Request createInviteRequest(String deviceId, String deviceIp, Integer devicePort, int rtpPort, String deviceRealIp) throws Exception {
        // 创建请求URI
        SipURI requestURI = addressFactory.createSipURI(deviceId, deviceIp + ":" + (devicePort != null ? devicePort : 5060));

        // 创建From头，使用公网IP
        String fromIp = properties.getNetwork().getSipExternalIp();
        SipURI fromURI = addressFactory.createSipURI(properties.getSip().getId(), fromIp + ":" + properties.getSip().getPort());
        Address fromAddress = addressFactory.createAddress(fromURI);
        FromHeader fromHeader = headerFactory.createFromHeader(fromAddress, "zlm" + System.currentTimeMillis());

        // 创建To头
        SipURI toURI = addressFactory.createSipURI(deviceId, deviceIp + ":" + (devicePort != null ? devicePort : 5060));
        Address toAddress = addressFactory.createAddress(toURI);
        ToHeader toHeader = headerFactory.createToHeader(toAddress, null);

        // 创建Via头，使用外部IP
        String viaIp = properties.getNetwork().getSipExternalIp();
        ViaHeader viaHeader = headerFactory.createViaHeader(viaIp, properties.getSip().getPort(), "TCP", null);
        java.util.List<ViaHeader> viaHeaders = new java.util.ArrayList<>();
        viaHeaders.add(viaHeader);

        // 创建Call-ID头
        CallIdHeader callIdHeader = sipProvider.getNewCallId();

        // 创建CSeq头
        CSeqHeader cSeqHeader = headerFactory.createCSeqHeader(1L, Request.INVITE);

        // 创建Max-Forwards头
        MaxForwardsHeader maxForwardsHeader = headerFactory.createMaxForwardsHeader(70);

        // 创建INVITE请求
        Request inviteRequest = messageFactory.createRequest(requestURI, Request.INVITE, callIdHeader, cSeqHeader, fromHeader, toHeader, viaHeaders, maxForwardsHeader);

        // 创建Contact头（必需），使用外部IP
        String contactIp = properties.getNetwork().getSipExternalIp();
        SipURI contactURI = addressFactory.createSipURI(properties.getSip().getId(), contactIp + ":" + properties.getSip().getPort());
        Address contactAddress = addressFactory.createAddress(contactURI);
        ContactHeader contactHeader = headerFactory.createContactHeader(contactAddress);
        inviteRequest.addHeader(contactHeader);

        // 添加User-Agent头
        inviteRequest.addHeader(headerFactory.createHeader("User-Agent", "GB28181 Server V2.0"));

        // 添加GB28181版本头
        inviteRequest.addHeader(headerFactory.createHeader("X-GB-Ver", "2.0"));

        // 添加Subject头（包含通道信息）
        String subject = String.format("%s:0000010000,%s:1", deviceId, properties.getSip().getId());
        inviteRequest.addHeader(headerFactory.createHeader("Subject", subject));

        // 创建SDP内容
        String sdp = createInviteSdp(rtpPort, deviceRealIp);
        ContentTypeHeader contentTypeHeader = headerFactory.createContentTypeHeader("application", "sdp");
        inviteRequest.setContent(sdp, contentTypeHeader);

        // 打印发送的INVITE请求
        log.info("发送INVITE请求到设备 {}: \n{}", deviceId, inviteRequest.toString());

        return inviteRequest;
    }

    /**
     * 创建INVITE请求的SDP内容
     */
    private String createInviteSdp(int rtpPort, String deviceIp) {
        // 统一从配置读取媒体IP，避免与SIP信令IP不一致
        String mediaIp = properties.getNetwork().getMediaIp();

        StringBuilder sdp = new StringBuilder();
        sdp.append("v=0\r\n");
        sdp.append("o=").append(properties.getSip().getId()).append(" 0 0 IN IP4 ").append(mediaIp).append("\r\n");
        sdp.append("s=Play\r\n");
        sdp.append("u=").append(properties.getSip().getId()).append(":0\r\n");
        sdp.append("c=IN IP4 ").append(mediaIp).append("\r\n");
        sdp.append("t=0 0\r\n");
        sdp.append("m=video ").append(rtpPort).append(" RTP/AVP 96 97 98\r\n");
        sdp.append("a=recvonly\r\n");
        sdp.append("a=setup:passive\r\n");
        sdp.append("a=connection:new\r\n");
        sdp.append("a=rtpmap:96 PS/90000\r\n");
        sdp.append("a=rtpmap:97 MPEG4/90000\r\n");
        sdp.append("a=rtpmap:98 H264/90000\r\n");
        sdp.append("a=downloadspeed:0\r\n");
        sdp.append("a=streamprofile:0\r\n");
        sdp.append("y=0000010000\r\n");

        return sdp.toString();
    }

    /**
     * 解析摄像机SDP并创建对应的RTP接收器
     */
    private void parseCameraSdpAndCreateReceiver(String deviceId, String sdp) {
        try {
            log.info("解析摄像机SDP: {}", sdp);

            // 解析SDP中的媒体信息
            String[] lines = sdp.split("\r\n");
            String mediaIp = null;
            int mediaPort = 0;

            for (String line : lines) {
                if (line.startsWith("c=IN IP4 ")) {
                    mediaIp = line.substring("c=IN IP4 ".length()).trim();
                } else if (line.startsWith("m=video ")) {
                    String[] parts = line.split(" ");
                    if (parts.length >= 2) {
                        mediaPort = Integer.parseInt(parts[1]);
                    }
                }
            }

            if (mediaIp != null && mediaPort > 0) {
                log.info("摄像机实际推流地址: {}:{}", mediaIp, mediaPort);

                // 如果摄像机使用内网地址推流，创建对应的接收器
                if (mediaIp.startsWith("192.168.")) {
                    // 创建内网RTP接收器
                    boolean success = zlmService.prepareRtpReceive(deviceId, mediaPort);
                    if (success) {
                        log.info("为摄像机内网推流创建RTP接收器成功: {}:{}", mediaIp, mediaPort);
                    }
                }
            }

        } catch (Exception e) {
            log.error("解析摄像机SDP失败: {}", deviceId, e);
        }
    }
}
