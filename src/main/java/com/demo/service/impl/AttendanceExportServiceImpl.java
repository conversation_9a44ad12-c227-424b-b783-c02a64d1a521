package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.entity.LeavePost;
import com.demo.entity.Overtime;
import com.demo.entity.Schedule;
import com.demo.entity.Shift;
import com.demo.entity.excel.AttendanceExportDTO;
import com.demo.entity.excel.AttendanceExportDetailDTO;
import com.demo.mapper.LeavePostMapper;
import com.demo.mapper.OvertimeMapper;
import com.demo.mapper.ScheduleMapper;
import com.demo.mapper.ShiftMapper;
import com.demo.mapper.DeviceStatusHistoryMapper;
import com.demo.entity.DeviceStatusHistory;
import com.demo.entity.Device;
import com.demo.mapper.DeviceMapper;
import com.demo.service.AttendanceExportService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import java.util.Collections;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 考勤数据导出服务实现类
 */
@Slf4j
@Service
public class AttendanceExportServiceImpl implements AttendanceExportService {

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private LeavePostMapper leavePostMapper;

    @Autowired
    private ShiftMapper shiftMapper;

    @Autowired
    private OvertimeMapper overtimeMapper;

    @Autowired
    private DeviceStatusHistoryMapper deviceStatusHistoryMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");



    /**
     * 设置Excel响应头
     */
    private void setExcelResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", 
                String.format("attachment;filename=%s.xlsx", encodedFileName));
    }

    /**
     * 获取所有符合条件的排班数据
     */
    private List<Schedule> getSchedules(
            String city, String county, String township, String hamlet, String site,
            LocalDate startDate, LocalDate endDate) {
        
        // 按地区条件查询相关用户的排班
        return scheduleMapper.getSchedulesByAreaAndDate(
                city, county, township, hamlet, site, startDate, endDate);
    }

    /**
     * 根据排班ID列表获取脱岗数据
     */
    private List<LeavePost> getLeavePosts(List<Integer> scheduleIds) {
        if (scheduleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<LeavePost> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(LeavePost::getScheduleId, scheduleIds);
        return leavePostMapper.selectList(wrapper);
    }

    /**
     * 获取所有班次数据
     */
    private Map<Integer, Shift> getShifts() {
        List<Shift> shifts = shiftMapper.selectList(null);
        return shifts.stream().collect(Collectors.toMap(Shift::getId, shift -> shift));
    }
    
    /**
     * 四舍五入保留两位小数
     */
    private double roundToTwoDecimals(double value) {
        return Math.round(value * 100.0) / 100.0;
    }
    
    /**
     * 写入Excel（指定表格名称）
     */
    private void writeExcel(HttpServletResponse response, List<AttendanceExportDTO> data, String sheetName) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = sheetName.contains("考勤") ? sheetName : (sheetName + "考勤汇总");
        String encodedFileName = URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
        
        // 确定显示级别 - 根据传入的区域参数或数据内容
        int displayLevel = 0;
        // 检查第一条数据来尝试确定显示级别
        if (!data.isEmpty()) {
            AttendanceExportDTO firstItem = data.get(0);
            // 尝试通过区域显示内容确定级别
            String area = firstItem.getArea();
            if (area != null && !area.isEmpty()) {
                // 检查是县级还是镇级或村级或站点
                String[] areaParts = area.split("-");
                if (areaParts.length == 1) {
                    // 只包含县名，显示县级
                    displayLevel = 1;
                } else {
                    // 根据具体的区域名结构确定级别
                    displayLevel = Math.min(areaParts.length, 4);
                }
            }
        }
        
        // 使用Excel原生API创建工作簿和表格，并添加标题行
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("考勤数据");
            
            // 创建标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 16);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            
            // 创建标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeight((short) 600);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(sheetName);
            titleCell.setCellStyle(titleStyle);
            
            // 根据显示级别调整表头
            List<String> headers = new ArrayList<>();
            
            // 根据显示级别动态添加地区列
            if (displayLevel == 1) {
                headers.add("区(县)");
            } else if (displayLevel == 2) {
                headers.add("镇");
            } else if (displayLevel == 3) {
                headers.add("村");
            } else if (displayLevel == 4) {
                headers.add("劝导站");
            } else {
                headers.add("地区");
            }
            
            // 固定的考勤数据列
            headers.add("员工姓名");
            headers.add("应上岗总时间");
            headers.add("实际上岗总时间");
            headers.add("在岗率(%)");
            headers.add("加班总时间");
            headers.add("掉线时长(小时)");
            headers.add("合计在岗时间");
            headers.add("合计在岗率(%)");
            
            // 合并标题行单元格 - 根据动态列数调整
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.size() - 1));
            
            // 创建表头样式
            XSSFCellStyle headerStyle = (XSSFCellStyle) workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            // 设置表头背景色为RGB(142,169,219)
            headerStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(142, 169, 219), null));
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            
            // 创建表头行
            Row headerRow = sheet.createRow(1);
            for (int i = 0; i < headers.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers.get(i));
                cell.setCellStyle(headerStyle);
            }
            
            // 创建数据样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            
            // 汇总行样式
            XSSFCellStyle summaryStyle = (XSSFCellStyle) workbook.createCellStyle();
            Font summaryFont = workbook.createFont();
            summaryFont.setBold(true);
            summaryStyle.setFont(summaryFont);
            // 设置汇总行背景色为RGB(189,215,238)
            summaryStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(189, 215, 238), null));
            summaryStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            summaryStyle.setBorderBottom(BorderStyle.THIN);
            summaryStyle.setBorderLeft(BorderStyle.THIN);
            summaryStyle.setBorderRight(BorderStyle.THIN);
            summaryStyle.setBorderTop(BorderStyle.THIN);
            
            // 写入数据
            int rowIndex = 2;
            for (AttendanceExportDTO item : data) {
                Row row = sheet.createRow(rowIndex++);
                int columnIndex = 0;
                
                // 使用适当的样式
                CellStyle style = Boolean.TRUE.equals(item.getIsSummary()) ? summaryStyle : dataStyle;
                
                // 区域列 - 根据显示级别确定内容
                Cell areaCell = row.createCell(columnIndex++);
                areaCell.setCellValue(item.getArea());
                areaCell.setCellStyle(style);
                
                // 员工姓名
                Cell nameCell = row.createCell(columnIndex++);
                nameCell.setCellValue(item.getUserName());
                nameCell.setCellStyle(style);
                
                // 应上岗总时间
                Cell scheduledCell = row.createCell(columnIndex++);
                scheduledCell.setCellValue(item.getScheduledTotalHours());
                scheduledCell.setCellStyle(style);
                
                // 实际上岗总时间
                Cell actualCell = row.createCell(columnIndex++);
                actualCell.setCellValue(item.getActualTotalHours());
                actualCell.setCellStyle(style);
                
                // 在岗率(%)
                Cell rateCell = row.createCell(columnIndex++);
                rateCell.setCellValue(item.getAttendanceRate());
                rateCell.setCellStyle(style);
                
                // 加班总时间
                Cell overtimeCell = row.createCell(columnIndex++);
                overtimeCell.setCellValue(item.getOvertimeTotalHours());
                overtimeCell.setCellStyle(style);
                
                // 设备掉线时长(小时)
                Cell offlineHoursCell = row.createCell(columnIndex++);
                offlineHoursCell.setCellValue(item.getDeviceOfflineHours() != null ? item.getDeviceOfflineHours() : 0.0);
                offlineHoursCell.setCellStyle(style);
                
                // 合计在岗时间 = 实际上岗总时间 + 加班总时间 + 设备掉线时长
                double totalOnDutyHours = (item.getActualTotalHours() != null ? item.getActualTotalHours() : 0.0)
                    + (item.getOvertimeTotalHours() != null ? item.getOvertimeTotalHours() : 0.0)
                    + (item.getDeviceOfflineHours() != null ? item.getDeviceOfflineHours() : 0.0);
                Cell totalHoursCell = row.createCell(columnIndex++);
                totalHoursCell.setCellValue(totalOnDutyHours);
                totalHoursCell.setCellStyle(style);
                
                // 合计在岗率(%)
                double scheduledHours = item.getScheduledTotalHours() != null ? item.getScheduledTotalHours() : 0.0;
                double totalAttendanceRate = scheduledHours > 0 ? Math.round((totalOnDutyHours / scheduledHours) * 10000.0) / 100.0 : 0.0;
                Cell totalRateCell = row.createCell(columnIndex++);
                totalRateCell.setCellValue(totalAttendanceRate);
                totalRateCell.setCellStyle(style);
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.size(); i++) {
                sheet.autoSizeColumn(i);
                // 设置最小列宽
                if (sheet.getColumnWidth(i) < 3000) {
                    sheet.setColumnWidth(i, 3000);
                }
                // 最大列宽限制
                if (sheet.getColumnWidth(i) > 8000) {
                    sheet.setColumnWidth(i, 8000);
                }
            }
            
            // 写入响应
            workbook.write(response.getOutputStream());
        }
    }

    @Override
    public void exportSummaryAttendance(
            String city, String county, String township, String hamlet, String site,
            LocalDate startDate, LocalDate endDate,
            HttpServletResponse response) throws IOException {

        try {
            // 构建标题 - 根据传入参数层级构建
            StringBuilder titleBuilder = new StringBuilder();
            
            // 根据传入的参数级别构建地区部分
            if (city != null && !city.isEmpty()) {
                titleBuilder.append(city);
                
            if (county != null && !county.isEmpty()) {
                    titleBuilder.append(county);
                    
            if (township != null && !township.isEmpty()) {
                        titleBuilder.append(township);
                        
            if (hamlet != null && !hamlet.isEmpty()) {
                            titleBuilder.append(hamlet);
                            
            if (site != null && !site.isEmpty()) {
                                titleBuilder.append(site);
                            }
                        }
                    }
                }
            }
            
            String title = String.format("%s劝导员考勤汇总数据（%s至%s）", 
                    titleBuilder.length() > 0 ? titleBuilder.toString() : "",
                    startDate.format(DATE_FORMATTER), endDate.format(DATE_FORMATTER));
            
            // 设置响应头
            setExcelResponseHeaders(response, title);

            // 获取所有符合条件的排班数据
            List<Schedule> schedules = getSchedules(city, county, township, hamlet, site, startDate, endDate);

            // 按地区级别组织排班数据
            Map<String, Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>>> organizedSchedules = 
                    organizeSchedulesByArea(schedules);

            // 获取所有排班ID列表
            List<Integer> scheduleIds = schedules.stream()
                    .map(Schedule::getId)
                    .collect(Collectors.toList());

            // 获取脱岗数据
            List<LeavePost> leavePosts = getLeavePosts(scheduleIds);
            Map<Integer, List<LeavePost>> leavePostsByScheduleId = leavePosts.stream()
                    .collect(Collectors.groupingBy(LeavePost::getScheduleId));

            // 获取班次数据
            Map<Integer, Shift> shiftsById = getShifts();

            // 确定汇总级别（根据传入的地区参数决定）
            int summaryLevel = determineSummaryLevel(city, county, township, hamlet, site);

            // 生成汇总数据
            List<AttendanceExportDTO> exportData = new ArrayList<>();
            
            // 遍历所有地区数据，只计算下一级的统计数据
            for (Map.Entry<String, Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>>> cityEntry : organizedSchedules.entrySet()) {
                String cityName = cityEntry.getKey();
                Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>> countySchedules = cityEntry.getValue();
                
                // 如果是市级查询，只汇总区县级数据
                if (summaryLevel == 1) {
                    for (Map.Entry<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>> countyEntry : countySchedules.entrySet()) {
                        String countyName = countyEntry.getKey();
                        Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = countyEntry.getValue();
                        
                        // 创建区县统计对象
                        CountyStats countyStats = new CountyStats(countyName);
                        
                        // 计算该区县下所有数据的统计
                        for (Map.Entry<String, Map<String, Map<Integer, List<Schedule>>>> townshipEntry : townshipSchedules.entrySet()) {
                            Map<String, Map<Integer, List<Schedule>>> hamletSchedules = townshipEntry.getValue();
                            
                            for (Map.Entry<String, Map<Integer, List<Schedule>>> hamletEntry : hamletSchedules.entrySet()) {
                                Map<Integer, List<Schedule>> userSchedules = hamletEntry.getValue();
                                
                                // 计算该村下所有用户的统计数据
                                for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                    List<Schedule> userScheduleList = userEntry.getValue();
                                    
                                    if (userScheduleList.isEmpty()) {
                                        continue;
                                    }
                                    
                                    // 计算用户统计数据
                                    EmployeeStats employeeStats = calculateEmployeeStats(
                                            userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                    
                                    // 累加到区县级统计
                                    countyStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                    countyStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                    countyStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                                }
                            }
                        }
                        
                        // 添加区县级汇总到结果
                        exportData.add(createTableFormatDto(countyName, "", "", countyName, countyStats));
                    }
                }
                // 如果是区县级查询，只汇总镇级数据
                else if (summaryLevel == 2) {
                    Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                            countySchedules.get(county);
                    
                    if (townshipSchedules != null) {
                        for (Map.Entry<String, Map<String, Map<Integer, List<Schedule>>>> townshipEntry : townshipSchedules.entrySet()) {
                            String townshipName = townshipEntry.getKey();
                            Map<String, Map<Integer, List<Schedule>>> hamletSchedules = townshipEntry.getValue();
                            
                            // 创建镇级统计对象
                            TownshipStats townshipStats = new TownshipStats(townshipName);
                            
                            // 计算该镇下所有数据的统计
                            for (Map.Entry<String, Map<Integer, List<Schedule>>> hamletEntry : hamletSchedules.entrySet()) {
                                Map<Integer, List<Schedule>> userSchedules = hamletEntry.getValue();
                                
                                // 计算该村下所有用户的统计数据
                                for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                    List<Schedule> userScheduleList = userEntry.getValue();
                                    
                                    if (userScheduleList.isEmpty()) {
                                        continue;
                                    }
                                    
                                    // 计算用户统计数据
                                    EmployeeStats employeeStats = calculateEmployeeStats(
                                            userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                    
                                    // 累加到镇级统计
                                    townshipStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                    townshipStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                    townshipStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                                }
                            }
                            
                            // 添加镇级汇总到结果
                            exportData.add(createTableFormatDto(county, townshipName, "", townshipName, townshipStats));
                        }
                    }
                }
                // 如果是镇级查询，只汇总村级数据
                else if (summaryLevel == 3) {
                    Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                            countySchedules.get(county);
                    
                    if (townshipSchedules != null) {
                        Map<String, Map<Integer, List<Schedule>>> hamletSchedules = 
                                townshipSchedules.get(township);
                        
                        if (hamletSchedules != null) {
                            for (Map.Entry<String, Map<Integer, List<Schedule>>> hamletEntry : hamletSchedules.entrySet()) {
                                String hamletName = hamletEntry.getKey();
                                Map<Integer, List<Schedule>> userSchedules = hamletEntry.getValue();
                                
                                // 创建村级统计对象
                                HamletStats hamletStats = new HamletStats(hamletName);
                                
                                // 计算该村下所有用户的统计数据
                                for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                    List<Schedule> userScheduleList = userEntry.getValue();
                                    
                                    if (userScheduleList.isEmpty()) {
                                        continue;
                                    }
                                    
                                    // 计算用户统计数据
                                    EmployeeStats employeeStats = calculateEmployeeStats(
                                            userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                    
                                    // 累加到村级统计
                                    hamletStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                    hamletStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                    hamletStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                                }
                                
                                // 添加村级汇总到结果
                                exportData.add(createTableFormatDto(county, township, hamletName, hamletName, hamletStats));
                            }
                        }
                    }
                }
                // 如果是村级查询，汇总点位或个人数据
                else if (summaryLevel >= 4) {
                    Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                            countySchedules.get(county);
                    
                    if (townshipSchedules != null) {
                        Map<String, Map<Integer, List<Schedule>>> hamletSchedules = 
                                townshipSchedules.get(township);
                        
                        if (hamletSchedules != null) {
                            Map<Integer, List<Schedule>> userSchedules = 
                                    hamletSchedules.get(hamlet);
                            
                            if (userSchedules != null) {
                                // 按站点分组
                                Map<String, List<Schedule>> siteSchedules = new HashMap<>();
                                
                                // 先将所有排班按站点分组
                                for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                    for (Schedule schedule : userEntry.getValue()) {
                                        String siteName = schedule.getSite() != null && !schedule.getSite().isEmpty() 
                                                ? schedule.getSite() : "未分配站点";
                                        siteSchedules
                                                .computeIfAbsent(siteName, k -> new ArrayList<>())
                                                .add(schedule);
                                    }
                                }
                                
                                // 处理每个站点的数据
                                for (Map.Entry<String, List<Schedule>> siteEntry : siteSchedules.entrySet()) {
                                    String siteName = siteEntry.getKey();
                                    List<Schedule> siteScheduleList = siteEntry.getValue();
                                    
                                    if (siteScheduleList.isEmpty()) {
                                        continue;
                                    }
                                    
                                    // 如果过滤了站点，只处理匹配的站点
                                    if (site != null && !site.isEmpty() && !siteName.equals(site)) {
                                        continue;
                                    }
                                    
                                    // 创建站点级统计对象
                                    SiteStats siteStats = new SiteStats(siteName);
                                    
                                    // 计算该站点的统计数据
                                    for (Schedule schedule : siteScheduleList) {
                                        List<Schedule> userScheduleList = Collections.singletonList(schedule);
                                    
                                    // 计算用户统计数据
                                    EmployeeStats employeeStats = calculateEmployeeStats(
                                            userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                    
                                        // 累加到站点级统计
                                        siteStats.addStats(employeeStats);
                                    }
                                    
                                    // 添加站点汇总到导出数据
                                    AttendanceExportDTO siteSummaryDto = createTableFormatDto(
                                            county, township, hamlet, siteName, siteStats);
                                    exportData.add(siteSummaryDto);
                                }
                            }
                        }
                    }
                }
            }

            // 导出Excel
            writeExcel(response, exportData, title);

        } catch (Exception e) {
            log.error("导出考勤汇总Excel失败", e);
            handleExportError(response, e);
        }
    }
    
    /**
     * 处理导出错误
     */
    private void handleExportError(HttpServletResponse response, Exception e) {
        log.error("导出Excel失败", e);
        // 不要重复写入响应，因为可能已经提交了部分数据
        // 由全局异常处理器负责处理
    }
    
    /**
     * 将排班数据按地区级别组织（市-区-镇-村-站点-用户ID）
     */
    private Map<String, Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>>> organizeSchedulesByArea(List<Schedule> schedules) {
        return schedules.stream()
                .collect(Collectors.groupingBy(s -> s.getCity() != null ? s.getCity() : "未分配",
                        Collectors.groupingBy(s -> s.getCounty() != null ? s.getCounty() : "未分配",
                                Collectors.groupingBy(s -> s.getTownship() != null ? s.getTownship() : "未分配",
                                        Collectors.groupingBy(s -> s.getHamlet() != null ? s.getHamlet() : "未分配",
                                                Collectors.groupingBy(Schedule::getUserId))))));
    }
    
    /**
     * 确定汇总级别（根据传入的地区参数决定）
     * 返回值：1-市级（汇总区县）, 2-区县级（汇总镇）, 3-镇级（汇总村）, 4-村级（汇总站点）, 5-站点级（汇总人员）
     */
    private int determineSummaryLevel(String city, String county, String township, String hamlet, String site) {
        if (city != null && !city.isEmpty()) {
            if (county != null && !county.isEmpty()) {
                if (township != null && !township.isEmpty()) {
                    if (hamlet != null && !hamlet.isEmpty()) {
                        if (site != null && !site.isEmpty()) {
                            return 5; // 站点级（汇总人员）
                        }
                        return 4; // 村级（汇总站点）
                    }
                    return 3; // 镇级（汇总村）
                }
                return 2; // 区县级（汇总镇）
            }
            return 1; // 市级（汇总区县）
        }
        return 0; // 未指定地区
    }
    
    /**
     * 计算单个员工的统计数据
     */
    private EmployeeStats calculateEmployeeStats(
            List<Schedule> schedules,
            Map<Integer, List<LeavePost>> leavePostsByScheduleId,
            Map<Integer, Shift> shiftsById,
            LocalDate queryStartDate,
            LocalDate queryEndDate) {
        
        EmployeeStats stats = new EmployeeStats();
        
        // 提前返回空结果，避免空指针异常
        if (schedules == null || schedules.isEmpty()) {
            return stats;
        }
        
        // 获取用户ID
        Integer userId = schedules.get(0).getUserId();
        
        // 统计掉线区间的最早和最晚时间
        LocalDateTime offlineStart = null;
        LocalDateTime offlineEnd = null;
        String city = null, county = null, township = null, hamlet = null, site = null;
        for (Schedule schedule : schedules) {
            Shift shift = shiftsById.get(schedule.getShiftId());
            if (shift == null) {
                continue;
            }
            // 计算应上岗时长
            LocalDateTime scheduleDate = schedule.getScheduleDate();
            LocalDate date = scheduleDate.toLocalDate();
            LocalTime startTime = shift.getStartTime();
            LocalTime endTime = shift.getEndTime();
            LocalDateTime scheduledStartTime = LocalDateTime.of(date, startTime);
            LocalDateTime scheduledEndTime = LocalDateTime.of(date, endTime);
            if (endTime.isBefore(startTime)) {
                scheduledEndTime = scheduledEndTime.plusDays(1);
            }
            long scheduledDurationMinutes = Duration.between(scheduledStartTime, scheduledEndTime).toMinutes();
            stats.scheduledTotalMinutes += scheduledDurationMinutes;
            // 获取脱岗信息
            List<LeavePost> scheduleLeavePosts = leavePostsByScheduleId.getOrDefault(schedule.getId(), new ArrayList<>());
            for (LeavePost leavePost : scheduleLeavePosts) {
                if (leavePost.getDuration() != null) {
                    stats.leavePostTotalMinutes += leavePost.getDuration();
                }
            }
            // 记录排班的最早和最晚时间、地区参数
            if (offlineStart == null || scheduledStartTime.isBefore(offlineStart)) offlineStart = scheduledStartTime;
            if (offlineEnd == null || scheduledEndTime.isAfter(offlineEnd)) offlineEnd = scheduledEndTime;
            if (city == null) city = schedule.getCity();
            if (county == null) county = schedule.getCounty();
            if (township == null) township = schedule.getTownship();
            if (hamlet == null) hamlet = schedule.getHamlet();
            if (site == null) site = schedule.getSite();
        }
        // 统计掉线次数和时长（只统计排班时间内）
        if (offlineStart != null && offlineEnd != null && site != null && !site.isEmpty()) {
            int[] offlineStats = calculateDeviceOfflineStats(city, county, township, hamlet, site, offlineStart, offlineEnd);
            stats.deviceOfflineCount = offlineStats[0];
            stats.deviceOfflineMinutes = offlineStats[1];
        }
        try {
            // 使用查询的日期范围获取加班记录，而不是排班日期
            if (queryStartDate != null && queryEndDate != null) {
                LocalDateTime startTime = queryStartDate.atStartOfDay();
                LocalDateTime endTime = queryEndDate.plusDays(1).atStartOfDay(); // 包含查询结束日期
                // 获取用户在整个查询时间段内的加班记录
                List<Overtime> overtimes = overtimeMapper.getUserOvertimeByTimeRange(userId, startTime, endTime);
                // 累加加班时长
                if (overtimes != null) {
                    for (Overtime overtime : overtimes) {
                        if (overtime.getDuration() != null) {
                            // 加班时长是以秒为单位存储的，需要转换为分钟
                            stats.overtimeTotalMinutes += overtime.getDuration() / 60.0;
                        }
                    }
                }
            } else {
                log.info("查询日期范围为空，无法获取完整加班记录");
            }
        } catch (Exception e) {
            // 如果发生异常，可能是加班表还未创建，忽略加班统计
            log.info("获取加班记录失败，可能是加班表还未创建: {}", e.getMessage());
        }
        return stats;
    }
    
    /**
     * 区域统计数据基类
     */
    private static abstract class AreaStats {
        double scheduledTotalMinutes = 0.0;
        double leavePostTotalMinutes = 0.0;
        double overtimeTotalMinutes = 0.0;
    }
    
    /**
     * 员工统计数据
     */
    private static class EmployeeStats extends AreaStats {
        int deviceOfflineCount = 0;
        int deviceOfflineMinutes = 0;
    }
    
    /**
     * 站点级统计数据
     */
    private static class SiteStats extends AreaStats {
        String site;
        int deviceOfflineCount = 0;
        int deviceOfflineMinutes = 0;
        
        public SiteStats(String site) {
            this.site = site;
        }
        
        public void addStats(EmployeeStats stats) {
            this.scheduledTotalMinutes += stats.scheduledTotalMinutes;
            this.leavePostTotalMinutes += stats.leavePostTotalMinutes;
            this.overtimeTotalMinutes += stats.overtimeTotalMinutes;
            this.deviceOfflineCount += stats.deviceOfflineCount;
            this.deviceOfflineMinutes += stats.deviceOfflineMinutes;
        }
    }
    
    /**
     * 村级统计数据
     */
    private static class HamletStats extends AreaStats {
        String hamlet;
        Map<String, SiteStats> siteStats = new HashMap<>();
        int deviceOfflineCount = 0;
        int deviceOfflineMinutes = 0;
        
        public HamletStats(String hamlet) {
            this.hamlet = hamlet;
        }
        
        public void addStats(SiteStats stats) {
            this.scheduledTotalMinutes += stats.scheduledTotalMinutes;
            this.leavePostTotalMinutes += stats.leavePostTotalMinutes;
            this.overtimeTotalMinutes += stats.overtimeTotalMinutes;
            this.deviceOfflineCount += stats.deviceOfflineCount;
            this.deviceOfflineMinutes += stats.deviceOfflineMinutes;
            siteStats.put(stats.site, stats);
        }
        
        public void addStats(EmployeeStats stats) {
            this.scheduledTotalMinutes += stats.scheduledTotalMinutes;
            this.leavePostTotalMinutes += stats.leavePostTotalMinutes;
            this.overtimeTotalMinutes += stats.overtimeTotalMinutes;
            this.deviceOfflineCount += stats.deviceOfflineCount;
            this.deviceOfflineMinutes += stats.deviceOfflineMinutes;
        }
    }
    
    /**
     * 镇级统计数据
     */
    private static class TownshipStats extends AreaStats {
        String township;
        Map<String, HamletStats> hamletStats = new HashMap<>();
        int deviceOfflineCount = 0;
        int deviceOfflineMinutes = 0;
        
        public TownshipStats(String township) {
            this.township = township;
        }
        
        public void addStats(HamletStats stats) {
            this.scheduledTotalMinutes += stats.scheduledTotalMinutes;
            this.leavePostTotalMinutes += stats.leavePostTotalMinutes;
            this.overtimeTotalMinutes += stats.overtimeTotalMinutes;
            this.deviceOfflineCount += stats.deviceOfflineCount;
            this.deviceOfflineMinutes += stats.deviceOfflineMinutes;
        }
    }
    
    /**
     * 区县级统计数据
     */
    private static class CountyStats extends AreaStats {
        String county;
        Map<String, TownshipStats> townshipStats = new HashMap<>();
        int deviceOfflineCount = 0;
        int deviceOfflineMinutes = 0;
        
        public CountyStats(String county) {
            this.county = county;
        }
        
        public void addStats(TownshipStats stats) {
            this.scheduledTotalMinutes += stats.scheduledTotalMinutes;
            this.leavePostTotalMinutes += stats.leavePostTotalMinutes;
            this.overtimeTotalMinutes += stats.overtimeTotalMinutes;
            this.deviceOfflineCount += stats.deviceOfflineCount;
            this.deviceOfflineMinutes += stats.deviceOfflineMinutes;
        }
    }
    
    @Override
    public void exportDetailAndSummary(
            String city, String county, String township, String hamlet, String site,
            LocalDate startDate, LocalDate endDate,
            HttpServletResponse response) throws IOException {

        try {
            // 构建标题 - 根据传入参数层级构建
            StringBuilder titleBuilder = new StringBuilder();
            
            // 根据传入的参数级别构建地区部分
            if (city != null && !city.isEmpty()) {
                titleBuilder.append(city);
                
                if (county != null && !county.isEmpty()) {
                    titleBuilder.append(county);
                    
                    if (township != null && !township.isEmpty()) {
                        titleBuilder.append(township);
                        
                        if (hamlet != null && !hamlet.isEmpty()) {
                            titleBuilder.append(hamlet);
                            
                            if (site != null && !site.isEmpty()) {
                                titleBuilder.append(site);
                            }
                        }
                    }
                }
            }
            
            String title = String.format("%s劝导员考勤明细和汇总数据（%s至%s）", 
                    titleBuilder.length() > 0 ? titleBuilder.toString() : "",
                    startDate.format(DATE_FORMATTER), endDate.format(DATE_FORMATTER));
            
            // 设置响应头
            setExcelResponseHeaders(response, title);

            // 获取所有符合条件的排班数据
            List<Schedule> schedules = getSchedules(city, county, township, hamlet, site, startDate, endDate);

            // 按地区级别组织排班数据
            Map<String, Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>>> organizedSchedules = 
                    organizeSchedulesByArea(schedules);

            // 获取所有排班ID列表
            List<Integer> scheduleIds = schedules.stream()
                    .map(Schedule::getId)
                    .collect(Collectors.toList());

            // 获取脱岗数据
            List<LeavePost> leavePosts = getLeavePosts(scheduleIds);
            Map<Integer, List<LeavePost>> leavePostsByScheduleId = leavePosts.stream()
                    .collect(Collectors.groupingBy(LeavePost::getScheduleId));

            // 获取班次数据
            Map<Integer, Shift> shiftsById = getShifts();

            // 确定汇总级别（根据传入的地区参数决定）
            int summaryLevel = determineSummaryLevel(city, county, township, hamlet, site);

            // 生成包含明细和汇总的数据
            List<AttendanceExportDetailDTO> exportData = 
                    generateDetailAndSummaryData(organizedSchedules, leavePostsByScheduleId, shiftsById, 
                            summaryLevel, city, county, township, hamlet, site, startDate, endDate);
            
            // 导出Excel
            writeDetailExcel(response, exportData, title);
            
        } catch (Exception e) {
            log.error("导出考勤明细和汇总Excel失败", e);
            handleExportError(response, e);
        }
    }
    
    /**
     * 生成包含明细和汇总的数据
     */
    private List<AttendanceExportDetailDTO> generateDetailAndSummaryData(
            Map<String, Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>>> organizedSchedules,
            Map<Integer, List<LeavePost>> leavePostsByScheduleId,
            Map<Integer, Shift> shiftsById,
            int summaryLevel,
            String inputCity, String inputCounty, String inputTownship, String inputHamlet, String inputSite,
            LocalDate startDate, LocalDate endDate) {
        
        List<AttendanceExportDetailDTO> exportData = new ArrayList<>();
        
        // 遍历地区数据
        for (Map.Entry<String, Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>>> cityEntry : organizedSchedules.entrySet()) {
            String cityName = cityEntry.getKey();
            
            // 如果设置了市筛选，则只处理匹配的市
            if (inputCity != null && !inputCity.isEmpty() && !cityName.equals(inputCity)) {
                    continue;
                }
                
            Map<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>> countySchedules = cityEntry.getValue();
            
            // 市级查询：添加区县明细和汇总，并添加人员明细
            if (summaryLevel == 1) {
                for (Map.Entry<String, Map<String, Map<String, Map<Integer, List<Schedule>>>>> countyEntry : countySchedules.entrySet()) {
                    String countyName = countyEntry.getKey();
                Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = countyEntry.getValue();
                
                    // 创建区县统计对象
                    CountyStats countyStats = new CountyStats(countyName);
                    
                    // 计算该区县下所有数据的统计
                    for (Map.Entry<String, Map<String, Map<Integer, List<Schedule>>>> townshipEntry : townshipSchedules.entrySet()) {
                        String townshipName = townshipEntry.getKey();
                        Map<String, Map<Integer, List<Schedule>>> hamletSchedules = townshipEntry.getValue();
                        
                        for (Map.Entry<String, Map<Integer, List<Schedule>>> hamletEntry : hamletSchedules.entrySet()) {
                            String hamletName = hamletEntry.getKey();
                            Map<Integer, List<Schedule>> userSchedules = hamletEntry.getValue();
                            
                            // 计算该村下所有用户的统计数据
                            for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                List<Schedule> userScheduleList = userEntry.getValue();
                                
                                if (userScheduleList.isEmpty()) {
                                    continue;
                                }
                                
                                // 计算用户统计数据
                                EmployeeStats employeeStats = calculateEmployeeStats(
                                        userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                
                                // 创建人员明细DTO，并添加到结果中
                                Schedule firstSchedule = userScheduleList.get(0);
                                AttendanceExportDetailDTO personDto = createDetailFormatDto(
                                        countyName, townshipName, hamletName, firstSchedule.getSite(),
                                        firstSchedule.getUserName(), employeeStats);
                                
                                // 根据查询级别设置显示的地区列（市级查询：显示区县、镇、村、站点）
                                personDto.setCountyDisplay(countyName);
                                personDto.setTownshipDisplay(townshipName);
                                personDto.setHamletDisplay(hamletName);
                                personDto.setSiteDisplay(firstSchedule.getSite());
                                
                                personDto.setIsSummary(false);
                                exportData.add(personDto);
                                
                                // 累加到区县级统计
                                countyStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                countyStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                countyStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                            }
                        }
                    }
                    
                    // 添加区县明细数据
                    AttendanceExportDetailDTO countyDto = createDetailFormatDto(
                            countyName, "", "", "", countyName, countyStats);
                    countyDto.setIsSummary(true);
                    
                    // 市级查询：区县汇总行只显示区县列
                    countyDto.setCountyDisplay(countyName);
                    countyDto.setTownshipDisplay("");
                    countyDto.setHamletDisplay("");
                    countyDto.setSiteDisplay("");
                    
                    exportData.add(countyDto);
                }
            }
            // 区县级查询：添加镇级明细和汇总，并添加人员明细
            else if (summaryLevel == 2) {
                Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                        countySchedules.get(inputCounty);
                
                if (townshipSchedules != null) {
                    for (Map.Entry<String, Map<String, Map<Integer, List<Schedule>>>> townshipEntry : townshipSchedules.entrySet()) {
                        String townshipName = townshipEntry.getKey();
                        Map<String, Map<Integer, List<Schedule>>> hamletSchedules = townshipEntry.getValue();
                        
                        // 创建镇级统计对象
                        TownshipStats townshipStats = new TownshipStats(townshipName);
                        
                        // 计算该镇下所有数据的统计
                        for (Map.Entry<String, Map<Integer, List<Schedule>>> hamletEntry : hamletSchedules.entrySet()) {
                            String hamletName = hamletEntry.getKey();
                            Map<Integer, List<Schedule>> userSchedules = hamletEntry.getValue();
                            
                            // 计算该村下所有用户的统计数据
                            for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                List<Schedule> userScheduleList = userEntry.getValue();
                                
                                if (userScheduleList.isEmpty()) {
                                    continue;
                                }
                                
                                // 计算用户统计数据
                                EmployeeStats employeeStats = calculateEmployeeStats(
                                        userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                
                                // 创建人员明细DTO，并添加到结果中
                                Schedule firstSchedule = userScheduleList.get(0);
                                AttendanceExportDetailDTO personDto = createDetailFormatDto(
                                        inputCounty, townshipName, hamletName, firstSchedule.getSite(),
                                        firstSchedule.getUserName(), employeeStats);
                                
                                // 根据查询级别设置显示的地区列（区县级查询：只显示镇、村、站点）
                                personDto.setCountyDisplay("");
                                personDto.setTownshipDisplay(townshipName);
                                personDto.setHamletDisplay(hamletName);
                                personDto.setSiteDisplay(firstSchedule.getSite());
                                
                                personDto.setIsSummary(false);
                                exportData.add(personDto);
                                
                                // 累加到镇级统计
                                townshipStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                townshipStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                townshipStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                            }
                        }
                        
                        // 添加镇级明细数据
                        AttendanceExportDetailDTO townshipDto = createDetailFormatDto(
                                inputCounty, townshipName, "", "", townshipName, townshipStats);
                        townshipDto.setIsSummary(true);
                        
                        // 区县级查询：镇汇总行仅显示镇列
                        townshipDto.setCountyDisplay("");
                        townshipDto.setTownshipDisplay(townshipName);
                        townshipDto.setHamletDisplay("");
                        townshipDto.setSiteDisplay("");
                        
                        exportData.add(townshipDto);
                    }
                }
            }
            // 镇级查询：添加村级明细和汇总，并添加人员明细
            else if (summaryLevel == 3) {
                Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                        countySchedules.get(inputCounty);
                
                if (townshipSchedules != null) {
                    Map<String, Map<Integer, List<Schedule>>> hamletSchedules = 
                            townshipSchedules.get(inputTownship);
                    
                    if (hamletSchedules != null) {
                        for (Map.Entry<String, Map<Integer, List<Schedule>>> hamletEntry : hamletSchedules.entrySet()) {
                            String hamletName = hamletEntry.getKey();
                            Map<Integer, List<Schedule>> userSchedules = hamletEntry.getValue();
                            
                            // 创建村级统计对象
                            HamletStats hamletStats = new HamletStats(hamletName);
                            
                            // 计算该村下所有用户的统计数据
                            for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                List<Schedule> userScheduleList = userEntry.getValue();
                                
                                if (userScheduleList.isEmpty()) {
                                    continue;
                                }
                                
                                // 计算用户统计数据
                                EmployeeStats employeeStats = calculateEmployeeStats(
                                        userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                
                                // 创建人员明细DTO，并添加到结果中
                                Schedule firstSchedule = userScheduleList.get(0);
                                AttendanceExportDetailDTO personDto = createDetailFormatDto(
                                        inputCounty, inputTownship, hamletName, firstSchedule.getSite(),
                                        firstSchedule.getUserName(), employeeStats);
                                
                                // 根据查询级别设置显示的地区列（镇级查询：只显示村、站点）
                                personDto.setCountyDisplay("");
                                personDto.setTownshipDisplay("");
                                personDto.setHamletDisplay(hamletName);
                                personDto.setSiteDisplay(firstSchedule.getSite());
                                
                                personDto.setIsSummary(false);
                                exportData.add(personDto);
                                
                                // 累加到村级统计
                                hamletStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                hamletStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                hamletStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                            }
                            
                            // 添加村级明细数据
                            AttendanceExportDetailDTO hamletDto = createDetailFormatDto(
                                    inputCounty, inputTownship, hamletName, "", hamletName, hamletStats);
                            hamletDto.setIsSummary(true);
                            
                            // 镇级查询：村汇总行仅显示村列
                            hamletDto.setCountyDisplay("");
                            hamletDto.setTownshipDisplay("");
                            hamletDto.setHamletDisplay(hamletName);
                            hamletDto.setSiteDisplay("");
                            
                            exportData.add(hamletDto);
                        }
                    }
                }
            }
            // 村级查询：添加劝导站明细和汇总，并添加人员明细
            else if (summaryLevel == 4) {
                Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                        countySchedules.get(inputCounty);
                
                if (townshipSchedules != null) {
                    Map<String, Map<Integer, List<Schedule>>> hamletSchedules = 
                            townshipSchedules.get(inputTownship);
                    
                    if (hamletSchedules != null) {
                        Map<Integer, List<Schedule>> userSchedules = 
                                hamletSchedules.get(inputHamlet);
                        
                        if (userSchedules != null) {
                            // 按站点分组
                            Map<String, List<Schedule>> siteSchedules = new HashMap<>();
                            Map<String, Map<Integer, List<Schedule>>> siteUserSchedules = new HashMap<>();
                            
                            // 先将所有排班按站点分组
                            for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                Integer userId = userEntry.getKey();
                                
                                for (Schedule schedule : userEntry.getValue()) {
                                    String siteName = schedule.getSite() != null && !schedule.getSite().isEmpty() 
                                            ? schedule.getSite() : "未分配站点";
                                    
                                    // 添加到站点的排班列表
                                    siteSchedules
                                            .computeIfAbsent(siteName, k -> new ArrayList<>())
                                            .add(schedule);
                                    
                                    // 添加到站点的用户排班分组
                                    Map<Integer, List<Schedule>> userScheduleMap = siteUserSchedules
                                            .computeIfAbsent(siteName, k -> new HashMap<>());
                                            
                                    userScheduleMap
                                            .computeIfAbsent(userId, k -> new ArrayList<>())
                                            .add(schedule);
                                }
                            }
                            
                            // 处理每个站点的数据
                            for (Map.Entry<String, List<Schedule>> siteEntry : siteSchedules.entrySet()) {
                                String siteName = siteEntry.getKey();
                                List<Schedule> siteScheduleList = siteEntry.getValue();
                                
                                if (siteScheduleList.isEmpty()) {
                            continue;
                        }
                        
                                // 如果过滤了站点，只处理匹配的站点
                                if (inputSite != null && !inputSite.isEmpty() && !siteName.equals(inputSite)) {
                                    continue;
                                }
                                
                                // 创建站点级统计对象
                                SiteStats siteStats = new SiteStats(siteName);
                                
                                // 先添加人员明细
                                Map<Integer, List<Schedule>> siteUserMap = siteUserSchedules.get(siteName);
                                if (siteUserMap != null) {
                                    for (Map.Entry<Integer, List<Schedule>> userEntry : siteUserMap.entrySet()) {
                                        List<Schedule> userScheduleList = userEntry.getValue();
                                        
                                        if (userScheduleList.isEmpty()) {
                                continue;
                            }
                            
                                        // 计算用户统计数据
                                        EmployeeStats employeeStats = calculateEmployeeStats(
                                                userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                        
                                        // 创建人员明细DTO，并添加到结果中
                                        Schedule firstSchedule = userScheduleList.get(0);
                                        AttendanceExportDetailDTO personDto = createDetailFormatDto(
                                                inputCounty, inputTownship, inputHamlet, siteName,
                                                firstSchedule.getUserName(), employeeStats);
                                        
                                        // 根据查询级别设置显示的地区列（村级查询：只显示站点）
                                        personDto.setCountyDisplay("");
                                        personDto.setTownshipDisplay("");
                                        personDto.setHamletDisplay("");
                                        personDto.setSiteDisplay(siteName);
                                        
                                        personDto.setIsSummary(false);
                                        exportData.add(personDto);
                                        
                                        // 累加到站点级统计
                                        siteStats.addStats(employeeStats);
                                    }
                                } else {
                                    // 如果没有用户数据，用原来的方式计算站点统计
                                    for (Schedule schedule : siteScheduleList) {
                                        List<Schedule> userScheduleList = Collections.singletonList(schedule);
                                        
                                        // 计算用户统计数据
                                        EmployeeStats employeeStats = calculateEmployeeStats(
                                                userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                        
                                        // 累加到站点级统计
                                        siteStats.addStats(employeeStats);
                                    }
                                }
                                
                                // 添加站点明细数据
                                AttendanceExportDetailDTO siteDto = createDetailFormatDto(
                                        inputCounty, inputTownship, inputHamlet, siteName, siteName, siteStats);
                                siteDto.setIsSummary(true);
                                
                                // 村级查询：站点汇总行仅显示站点列
                                siteDto.setCountyDisplay("");
                                siteDto.setTownshipDisplay("");
                                siteDto.setHamletDisplay("");
                                siteDto.setSiteDisplay(siteName);
                                
                                exportData.add(siteDto);
                            }
                        }
                    }
                }
            }
            // 站点级查询：添加人员明细和站点汇总
            else if (summaryLevel == 5) {
                Map<String, Map<String, Map<Integer, List<Schedule>>>> townshipSchedules = 
                        countySchedules.get(inputCounty);
                
                if (townshipSchedules != null) {
                    Map<String, Map<Integer, List<Schedule>>> hamletSchedules = 
                            townshipSchedules.get(inputTownship);
                    
                    if (hamletSchedules != null) {
                        Map<Integer, List<Schedule>> userSchedules = hamletSchedules.get(inputHamlet);
                        
                        if (userSchedules != null) {
                            // 创建站点级统计对象
                            SiteStats siteStats = new SiteStats(inputSite);
                            
                            // 处理所有用户数据
                            for (Map.Entry<Integer, List<Schedule>> userEntry : userSchedules.entrySet()) {
                                List<Schedule> userScheduleList = new ArrayList<>();
                                
                                // 过滤出站点匹配的排班记录
                                for (Schedule schedule : userEntry.getValue()) {
                                    if (schedule.getSite() != null && schedule.getSite().equals(inputSite)) {
                                        userScheduleList.add(schedule);
                                    }
                                }
                                
                                if (userScheduleList.isEmpty()) {
                                    continue;
                                }
                                
                                // 计算用户统计数据
                                EmployeeStats employeeStats = calculateEmployeeStats(
                                        userScheduleList, leavePostsByScheduleId, shiftsById, startDate, endDate);
                                
                                // 创建人员明细DTO
                                Schedule firstSchedule = userScheduleList.get(0);
                                AttendanceExportDetailDTO personDto = createDetailFormatDto(
                                        inputCounty, inputTownship, inputHamlet, inputSite,
                                        firstSchedule.getUserName(), employeeStats);
                                
                                // 站点级查询：已选定站点，所有地区列都不需要
                                personDto.setCountyDisplay("");
                                personDto.setTownshipDisplay("");
                                personDto.setHamletDisplay("");
                                personDto.setSiteDisplay("");
                                
                                personDto.setIsSummary(false);
                                
                                // 将人员明细添加到导出列表
                                exportData.add(personDto);
                                
                                // 累加到站点级统计
                                siteStats.scheduledTotalMinutes += employeeStats.scheduledTotalMinutes;
                                siteStats.leavePostTotalMinutes += employeeStats.leavePostTotalMinutes;
                                siteStats.overtimeTotalMinutes += employeeStats.overtimeTotalMinutes;
                            }
                            
                            // 添加站点汇总数据
                            AttendanceExportDetailDTO siteSummaryDto = createDetailFormatDto(
                                    inputCounty, inputTownship, inputHamlet, inputSite, inputSite, siteStats);
                            siteSummaryDto.setIsSummary(true);
                            
                            // 站点级查询：汇总行也不显示地区
                            siteSummaryDto.setCountyDisplay("");
                            siteSummaryDto.setTownshipDisplay("");
                            siteSummaryDto.setHamletDisplay("");
                            siteSummaryDto.setSiteDisplay("");
                            
                            exportData.add(siteSummaryDto);
                        }
                    }
                }
            }
        }
        
        return exportData;
    }
    
    /**
     * 确定地区显示文本
     */
    private String determineAreaDisplay(String county, String township, String hamlet, String site) {
        // 由于我们现在使用单独的字段展示，这个方法只用于内部处理
        StringBuilder display = new StringBuilder();
        
        if (county != null && !county.isEmpty()) {
            display.append(county);
        }
        
        if (township != null && !township.isEmpty()) {
            if (display.length() > 0) {
                display.append("-");
            }
            display.append(township);
        }
        
        if (hamlet != null && !hamlet.isEmpty()) {
            if (display.length() > 0) {
                display.append("-");
            }
            display.append(hamlet);
        }
        
        if (site != null && !site.isEmpty()) {
            if (display.length() > 0) {
                display.append("-");
            }
            display.append(site);
        }
        
        return display.toString();
    }
    
    /**
     * 创建详细格式的DTO（用于明细和汇总导出）
     */
    private AttendanceExportDetailDTO createDetailFormatDto(
            String county, String township, String hamlet, String site, String displayName, AreaStats stats) {
        AttendanceExportDetailDTO dto = new AttendanceExportDetailDTO();
        
        // 设置区域信息（内部处理用）
        dto.setCounty(county);
        dto.setTownship(township);
        dto.setHamlet(hamlet);
        dto.setSite(site);
        dto.setUserName(displayName);
        dto.setIsSummary(true);
        
        // 设置显示的区域信息 - 初始化，调用处会根据查询级别调整显示内容
        dto.setCountyDisplay(county);
        dto.setTownshipDisplay(township);
        dto.setHamletDisplay(hamlet);
        dto.setSiteDisplay(site);
        
        // 保留原有区域显示格式（内部使用）
        dto.setArea(determineAreaDisplay(county, township, hamlet, site));
        
        // 设置统计数据
        setStatsToDetailDto(dto, stats);
        
        return dto;
    }
    
    /**
     * 将统计数据设置到详细格式DTO
     */
    private void setStatsToDetailDto(AttendanceExportDetailDTO dto, AreaStats stats) {
        // 计算基本时间数据
        double scheduledHours = roundToTwoDecimals(stats.scheduledTotalMinutes / 60.0);
        double actualTotalHours = Math.max(0, (stats.scheduledTotalMinutes - stats.leavePostTotalMinutes) / 60.0);
        actualTotalHours = roundToTwoDecimals(actualTotalHours);
        double leavePostHours = roundToTwoDecimals(stats.leavePostTotalMinutes / 60.0);
        double overtimeHours = roundToTwoDecimals(stats.overtimeTotalMinutes / 60.0);
        
        // 设置基本值
        dto.setScheduledTotalHours(scheduledHours);
        dto.setActualTotalHours(actualTotalHours);
        dto.setLeavePostTotalHours(leavePostHours);
        dto.setOvertimeTotalHours(overtimeHours);
        
        // 计算并设置新增字段
        // 1. 合计在岗时间
        double totalOnDutyHours = roundToTwoDecimals(actualTotalHours + overtimeHours);
        dto.setTotalOnDutyHours(totalOnDutyHours);
        
        // 2. 计算在岗率
        if (scheduledHours > 0) {
            double attendanceRate = roundToTwoDecimals((actualTotalHours / scheduledHours) * 100);
            dto.setAttendanceRate(attendanceRate);
            
            // 3. 计算合计在岗率
            double totalAttendanceRate = roundToTwoDecimals((totalOnDutyHours / scheduledHours) * 100);
            dto.setTotalAttendanceRate(totalAttendanceRate);
        } else {
            dto.setAttendanceRate(0.0);
            dto.setTotalAttendanceRate(0.0);
        }
        if (stats instanceof EmployeeStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((EmployeeStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof SiteStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((SiteStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof HamletStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((HamletStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof TownshipStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((TownshipStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof CountyStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((CountyStats) stats).deviceOfflineMinutes / 60.0));
        }
    }
    
    /**
     * 写入明细和汇总Excel
     */
    private void writeDetailExcel(HttpServletResponse response, List<AttendanceExportDetailDTO> data, String sheetName) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = sheetName.contains("考勤") ? sheetName : (sheetName + "考勤汇总");
        String encodedFileName = URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
        
        // 确定显示级别
        int displayLevel = 0;
        // 检查第一条数据来确定显示级别
        if (!data.isEmpty()) {
            AttendanceExportDetailDTO firstItem = data.get(0);
            if (!firstItem.getCountyDisplay().isEmpty()) {
                displayLevel = 1; // 显示县级
            } else if (!firstItem.getTownshipDisplay().isEmpty()) {
                displayLevel = 2; // 显示镇级
            } else if (!firstItem.getHamletDisplay().isEmpty()) {
                displayLevel = 3; // 显示村级
            } else if (!firstItem.getSiteDisplay().isEmpty()) {
                displayLevel = 4; // 显示站点级
            } else {
                displayLevel = 5; // 仅显示人员
            }
        }
        
        // 使用Excel原生API创建工作簿和表格，并添加标题行
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("考勤数据");
            
            // 创建标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 16);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            
            // 创建标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeight((short) 600);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(sheetName);
            titleCell.setCellStyle(titleStyle);
            
            // 根据显示级别调整表头
            List<String> headers = new ArrayList<>();
            int headerColumns = 0;
            
            // 根据显示级别动态添加地区列
            if (displayLevel == 1) {
                headers.add("区(县)");
                headers.add("镇");
                headers.add("村");
                headers.add("劝导站");
                headerColumns = 4;
            } else if (displayLevel == 2) {
                headers.add("镇");
                headers.add("村");
                headers.add("劝导站");
                headerColumns = 3;
            } else if (displayLevel == 3) {
                headers.add("村");
                headers.add("劝导站");
                headerColumns = 2;
            } else if (displayLevel == 4) {
                headers.add("劝导站");
                headerColumns = 1;
            } else {
                headerColumns = 0;
            }
            
            // 固定的考勤数据列
            headers.add("员工姓名");
            headers.add("应上岗总时间");
            headers.add("实际上岗总时间");
            headers.add("在岗率(%)");
            headers.add("加班总时间");
            headers.add("掉线时长(小时)");
            headers.add("合计在岗时间");
            headers.add("合计在岗率(%)");
            
            // 合并标题行单元格 - 根据动态列数调整
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.size() - 1));
            
            // 创建表头样式
            XSSFCellStyle headerStyle = (XSSFCellStyle) workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            // 设置表头背景色为RGB(142,169,219)
            headerStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(142, 169, 219), null));
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            
            // 创建表头行
            Row headerRow = sheet.createRow(1);
            for (int i = 0; i < headers.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers.get(i));
                cell.setCellStyle(headerStyle);
            }
            
            // 创建数据样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            
            // 汇总行样式
            XSSFCellStyle summaryStyle = (XSSFCellStyle) workbook.createCellStyle();
            Font summaryFont = workbook.createFont();
            summaryFont.setBold(true);
            summaryStyle.setFont(summaryFont);
            // 设置汇总行背景色为RGB(189,215,238)
            summaryStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(189, 215, 238), null));
            summaryStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            summaryStyle.setBorderBottom(BorderStyle.THIN);
            summaryStyle.setBorderLeft(BorderStyle.THIN);
            summaryStyle.setBorderRight(BorderStyle.THIN);
            summaryStyle.setBorderTop(BorderStyle.THIN);
            
            // 写入数据
            int rowIndex = 2;
            for (AttendanceExportDetailDTO item : data) {
                Row row = sheet.createRow(rowIndex++);
                
                // 使用适当的样式
                CellStyle style = Boolean.TRUE.equals(item.getIsSummary()) ? summaryStyle : dataStyle;
                
                // 根据显示级别动态创建地区列
                int columnIndex = 0;
                
                // 根据显示级别动态添加地区列
                if (displayLevel == 1) {
                    // 区(县)
                    Cell countyCell = row.createCell(columnIndex++);
                    countyCell.setCellValue(item.getCountyDisplay());
                    countyCell.setCellStyle(style);
                    
                    // 镇
                    Cell townshipCell = row.createCell(columnIndex++);
                    townshipCell.setCellValue(item.getTownshipDisplay());
                    townshipCell.setCellStyle(style);
                    
                    // 村
                    Cell hamletCell = row.createCell(columnIndex++);
                    hamletCell.setCellValue(item.getHamletDisplay());
                    hamletCell.setCellStyle(style);
                    
                    // 劝导站
                    Cell siteCell = row.createCell(columnIndex++);
                    siteCell.setCellValue(item.getSiteDisplay());
                    siteCell.setCellStyle(style);
                } else if (displayLevel == 2) {
                    // 镇
                    Cell townshipCell = row.createCell(columnIndex++);
                    townshipCell.setCellValue(item.getTownshipDisplay());
                    townshipCell.setCellStyle(style);
                    
                    // 村
                    Cell hamletCell = row.createCell(columnIndex++);
                    hamletCell.setCellValue(item.getHamletDisplay());
                    hamletCell.setCellStyle(style);
                    
                    // 劝导站
                    Cell siteCell = row.createCell(columnIndex++);
                    siteCell.setCellValue(item.getSiteDisplay());
                    siteCell.setCellStyle(style);
                } else if (displayLevel == 3) {
                    // 村
                    Cell hamletCell = row.createCell(columnIndex++);
                    hamletCell.setCellValue(item.getHamletDisplay());
                    hamletCell.setCellStyle(style);
                    
                    // 劝导站
                    Cell siteCell = row.createCell(columnIndex++);
                    siteCell.setCellValue(item.getSiteDisplay());
                    siteCell.setCellStyle(style);
                } else if (displayLevel == 4) {
                    // 劝导站
                    Cell siteCell = row.createCell(columnIndex++);
                    siteCell.setCellValue(item.getSiteDisplay());
                    siteCell.setCellStyle(style);
                }
                
                // 员工姓名
                Cell nameCell = row.createCell(columnIndex++);
                nameCell.setCellValue(item.getUserName());
                nameCell.setCellStyle(style);
                
                // 应上岗总时间
                Cell scheduledCell = row.createCell(columnIndex++);
                scheduledCell.setCellValue(item.getScheduledTotalHours());
                scheduledCell.setCellStyle(style);
                
                // 实际上岗总时间
                Cell actualCell = row.createCell(columnIndex++);
                actualCell.setCellValue(item.getActualTotalHours());
                actualCell.setCellStyle(style);
                
                // 在岗率(%)
                Cell rateCell = row.createCell(columnIndex++);
                rateCell.setCellValue(item.getAttendanceRate());
                rateCell.setCellStyle(style);
                
                // 加班总时间
                Cell overtimeCell = row.createCell(columnIndex++);
                overtimeCell.setCellValue(item.getOvertimeTotalHours());
                overtimeCell.setCellStyle(style);
                
                // 设备掉线时长(小时)
                Cell offlineHoursCell = row.createCell(columnIndex++);
                offlineHoursCell.setCellValue(item.getDeviceOfflineHours() != null ? item.getDeviceOfflineHours() : 0.0);
                offlineHoursCell.setCellStyle(style);
                
                // 合计在岗时间 = 实际上岗总时间 + 加班总时间 + 设备掉线时长
                double totalOnDutyHours = (item.getActualTotalHours() != null ? item.getActualTotalHours() : 0.0)
                    + (item.getOvertimeTotalHours() != null ? item.getOvertimeTotalHours() : 0.0)
                    + (item.getDeviceOfflineHours() != null ? item.getDeviceOfflineHours() : 0.0);
                Cell totalHoursCell = row.createCell(columnIndex++);
                totalHoursCell.setCellValue(totalOnDutyHours);
                totalHoursCell.setCellStyle(style);
                
                // 合计在岗率(%)
                double scheduledHours = item.getScheduledTotalHours() != null ? item.getScheduledTotalHours() : 0.0;
                double totalAttendanceRate = scheduledHours > 0 ? Math.round((totalOnDutyHours / scheduledHours) * 10000.0) / 100.0 : 0.0;
                Cell totalRateCell = row.createCell(columnIndex++);
                totalRateCell.setCellValue(totalAttendanceRate);
                totalRateCell.setCellStyle(style);
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.size(); i++) {
                sheet.autoSizeColumn(i);
                // 设置最小列宽
                if (sheet.getColumnWidth(i) < 3000) {
                    sheet.setColumnWidth(i, 3000);
                }
                // 最大列宽限制
                if (sheet.getColumnWidth(i) > 8000) {
                    sheet.setColumnWidth(i, 8000);
                }
            }
            
            // 写入响应
            workbook.write(response.getOutputStream());
        }
    }

    @Override
    public void exportPersonalAttendance(
            String city, String county, String township, String hamlet, String site,
            LocalDate startDate, LocalDate endDate,
            HttpServletResponse response) throws IOException {

        try {
            // 构建标题 - 根据传入参数层级构建
            StringBuilder titleBuilder = new StringBuilder();
            
            // 根据传入的参数级别构建地区部分
            if (city != null && !city.isEmpty()) {
                titleBuilder.append(city);
                
                if (county != null && !county.isEmpty()) {
                    titleBuilder.append(county);
                    
                    if (township != null && !township.isEmpty()) {
                        titleBuilder.append(township);
                        
                        if (hamlet != null && !hamlet.isEmpty()) {
                            titleBuilder.append(hamlet);
                            
                            if (site != null && !site.isEmpty()) {
                                titleBuilder.append(site);
                            }
                        }
                    }
                }
            }
            
            String title = String.format("%s劝导员考勤明细数据（%s至%s）", 
                    titleBuilder.length() > 0 ? titleBuilder.toString() : "",
                    startDate.format(DATE_FORMATTER), endDate.format(DATE_FORMATTER));
            
            // 设置响应头
            setExcelResponseHeaders(response, title);

            // 获取所有符合条件的排班数据
            List<Schedule> schedules = getSchedules(city, county, township, hamlet, site, startDate, endDate);

            // 获取所有排班ID列表
            List<Integer> scheduleIds = schedules.stream()
                    .map(Schedule::getId)
                    .collect(Collectors.toList());

            // 获取脱岗数据
            List<LeavePost> leavePosts = getLeavePosts(scheduleIds);
            Map<Integer, List<LeavePost>> leavePostsByScheduleId = leavePosts.stream()
                    .collect(Collectors.groupingBy(LeavePost::getScheduleId));

            // 获取班次数据
            Map<Integer, Shift> shiftsById = getShifts();

            // 生成明细数据
            List<AttendanceExportDetailDTO> exportData = new ArrayList<>();
            
            // 确定显示级别
            int displayLevel = determineSummaryLevel(city, county, township, hamlet, site);
            
            // 按人员分组处理排班数据
            Map<Integer, List<Schedule>> schedulesGroupByUser = schedules.stream()
                    .collect(Collectors.groupingBy(Schedule::getUserId));
            
            // 生成每个人员的考勤明细
            for (Map.Entry<Integer, List<Schedule>> entry : schedulesGroupByUser.entrySet()) {
                List<Schedule> userSchedules = entry.getValue();
                
                if (userSchedules.isEmpty()) {
                    continue;
                }
                
                // 排序排班记录，按日期升序
                userSchedules.sort((s1, s2) -> s1.getScheduleDate().compareTo(s2.getScheduleDate()));
                
                // 获取用户信息
                Schedule firstSchedule = userSchedules.get(0);
                String userName = firstSchedule.getUserName();
                String userCounty = firstSchedule.getCounty();
                String userTownship = firstSchedule.getTownship();
                String userHamlet = firstSchedule.getHamlet();
                String userSite = firstSchedule.getSite();
                
                // 计算用户统计数据
                EmployeeStats employeeStats = calculateEmployeeStats(
                        userSchedules, leavePostsByScheduleId, shiftsById, startDate, endDate);
                
                // 创建导出DTO - 使用DetailDTO而不是简单DTO
                AttendanceExportDetailDTO dto = new AttendanceExportDetailDTO();
                
                // 设置人员信息
                dto.setUserName(userName);
                
                // 根据查询级别设置显示的地区列
                if (displayLevel == 1) { // 市级查询，显示县、镇、村、站点
                    dto.setCountyDisplay(userCounty);
                    dto.setTownshipDisplay(userTownship);
                    dto.setHamletDisplay(userHamlet);
                    dto.setSiteDisplay(userSite);
                } else if (displayLevel == 2) { // 县级查询，显示镇、村、站点
                    dto.setCountyDisplay("");
                    dto.setTownshipDisplay(userTownship);
                    dto.setHamletDisplay(userHamlet);
                    dto.setSiteDisplay(userSite);
                } else if (displayLevel == 3) { // 镇级查询，显示村、站点
                    dto.setCountyDisplay("");
                    dto.setTownshipDisplay("");
                    dto.setHamletDisplay(userHamlet);
                    dto.setSiteDisplay(userSite);
                } else if (displayLevel == 4) { // 村级查询，显示站点
                    dto.setCountyDisplay("");
                    dto.setTownshipDisplay("");
                    dto.setHamletDisplay("");
                    dto.setSiteDisplay(userSite);
                } else if (displayLevel == 5) { // 站点级查询，不显示地区
                    dto.setCountyDisplay("");
                    dto.setTownshipDisplay("");
                    dto.setHamletDisplay("");
                    dto.setSiteDisplay("");
                }
                
                // 保存原始区域信息（用于内部处理）
                dto.setCounty(userCounty);
                dto.setTownship(userTownship);
                dto.setHamlet(userHamlet);
                dto.setSite(userSite);
                dto.setArea(determineAreaDisplay(userCounty, userTownship, userHamlet, userSite));
                
                // 设置考勤统计数据
                // 计算基本时间数据
                double scheduledHours = roundToTwoDecimals(employeeStats.scheduledTotalMinutes / 60.0);
                double actualTotalHours = Math.max(0, (employeeStats.scheduledTotalMinutes - employeeStats.leavePostTotalMinutes) / 60.0);
                actualTotalHours = roundToTwoDecimals(actualTotalHours);
                double overtimeHours = roundToTwoDecimals(employeeStats.overtimeTotalMinutes / 60.0);
                
                // 设置基本值
                dto.setScheduledTotalHours(scheduledHours);
                dto.setActualTotalHours(actualTotalHours);
                double leavePostHours = roundToTwoDecimals(employeeStats.leavePostTotalMinutes / 60.0);
                dto.setLeavePostTotalHours(leavePostHours);
                dto.setOvertimeTotalHours(overtimeHours);
                
                // 计算并设置衍生字段
                // 1. 合计在岗时间
                double totalOnDutyHours = roundToTwoDecimals(actualTotalHours + overtimeHours);
                dto.setTotalOnDutyHours(totalOnDutyHours);
                
                // 2. 计算在岗率
                if (scheduledHours > 0) {
                    double attendanceRate = roundToTwoDecimals((actualTotalHours / scheduledHours) * 100);
                    dto.setAttendanceRate(attendanceRate);
                    
                    // 3. 计算合计在岗率
                    double totalAttendanceRate = roundToTwoDecimals((totalOnDutyHours / scheduledHours) * 100);
                    dto.setTotalAttendanceRate(totalAttendanceRate);
                } else {
                    dto.setAttendanceRate(0.0);
                    dto.setTotalAttendanceRate(0.0);
                }
                
                // 添加到导出数据列表
                dto.setDeviceOfflineHours(roundToTwoDecimals(employeeStats.deviceOfflineMinutes / 60.0));
                exportData.add(dto);
            }
            
            // 写入Excel，使用明细格式
            writeDetailExcel(response, exportData, title);
            
        } catch (Exception e) {
            log.error("导出考勤明细Excel失败", e);
            handleExportError(response, e);
        }
    }

    /**
     * 创建表格格式的DTO（用于汇总导出）
     */
    private AttendanceExportDTO createTableFormatDto(
            String county, String township, String hamlet, String displayName, AreaStats stats) {
        AttendanceExportDTO dto = new AttendanceExportDTO();
        
        // 设置区域信息
        dto.setCounty(county);
        dto.setTownship(township);
        dto.setHamlet(hamlet);
        dto.setArea(displayName);
        dto.setIsSummary(true);
        
        // 设置统计数据
        // 计算基本时间数据
        double scheduledHours = roundToTwoDecimals(stats.scheduledTotalMinutes / 60.0);
        double actualTotalHours = Math.max(0, (stats.scheduledTotalMinutes - stats.leavePostTotalMinutes) / 60.0);
        actualTotalHours = roundToTwoDecimals(actualTotalHours);
        double leavePostHours = roundToTwoDecimals(stats.leavePostTotalMinutes / 60.0);
        double overtimeHours = roundToTwoDecimals(stats.overtimeTotalMinutes / 60.0);
        
        // 设置基本值
        dto.setScheduledTotalHours(scheduledHours);
        dto.setActualTotalHours(actualTotalHours);
        dto.setLeavePostTotalHours(leavePostHours);
        dto.setOvertimeTotalHours(overtimeHours);
        
        // 计算并设置新增字段
        // 1. 合计在岗时间
        double totalOnDutyHours = roundToTwoDecimals(actualTotalHours + overtimeHours);
        dto.setTotalOnDutyHours(totalOnDutyHours);
        
        // 2. 计算在岗率
        if (scheduledHours > 0) {
            double attendanceRate = roundToTwoDecimals((actualTotalHours / scheduledHours) * 100);
            dto.setAttendanceRate(attendanceRate);
            
            // 3. 计算合计在岗率
            double totalAttendanceRate = roundToTwoDecimals((totalOnDutyHours / scheduledHours) * 100);
            dto.setTotalAttendanceRate(totalAttendanceRate);
        } else {
            dto.setAttendanceRate(0.0);
            dto.setTotalAttendanceRate(0.0);
        }
        
        if (stats instanceof EmployeeStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((EmployeeStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof SiteStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((SiteStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof HamletStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((HamletStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof TownshipStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((TownshipStats) stats).deviceOfflineMinutes / 60.0));
        } else if (stats instanceof CountyStats) {
            dto.setDeviceOfflineHours(roundToTwoDecimals(((CountyStats) stats).deviceOfflineMinutes / 60.0));
        }
        
        return dto;
    }

    /**
     * 计算加班时长 - 处理仅有加班记录没有排班记录的情况
     * @param userId 用户ID
     * @param userName 用户姓名
     * @param city 市
     * @param county 县
     * @param township 镇
     * @param hamlet 村
     * @param site 点位
     * @param overtimes 加班记录列表
     * @return 员工统计数据
     */
    private EmployeeStats calculateOvertimeOnlyStats(
            Integer userId, String userName, 
            String city, String county, String township, String hamlet, String site,
            List<Overtime> overtimes) {
        
        EmployeeStats stats = new EmployeeStats();
        
        // 提前返回空结果，避免空指针异常
        if (overtimes == null || overtimes.isEmpty()) {
            return stats;
        }
        
        // 累加加班时长
        for (Overtime overtime : overtimes) {
            if (overtime.getDuration() != null) {
                // 加班时长是以秒为单位存储的，需要转换为分钟
                stats.overtimeTotalMinutes += overtime.getDuration() / 60.0;
            }
        }
        
        return stats;
    }

    // 统计排班时间内电脑设备掉线次数和时长（分钟）
    private int[] calculateDeviceOfflineStats(String city, String county, String township, String hamlet, String site, LocalDateTime start, LocalDateTime end) {
//        log.info("[掉线统计] 查询参数: city={}, county={}, township={}, hamlet={}, site={}, start={}, end={}", city, county, township, hamlet, site, start, end);
        // 查询该点位所有电脑设备
        List<Device> devices = deviceMapper.selectList(new QueryWrapper<Device>()
                .eq("city", city)
                .eq("county", county)
                .eq("township", township)
                .eq("hamlet", hamlet)
                .eq("site", site)
                .eq("device_type", "电脑"));
//        log.info("[掉线统计] 查询到电脑设备数量: {}", devices.size());
        if (devices.isEmpty()) return new int[]{0, 0};
        int totalCount = 0;
        int totalMinutes = 0;
        for (Device device : devices) {
//            log.info("[掉线统计] 设备: 编号={}, IP={}", device.getEquipmentNumber(), device.getIp());
            List<DeviceStatusHistory> offlineList = deviceStatusHistoryMapper.selectList(new QueryWrapper<DeviceStatusHistory>()
                    .eq("equipment_number", device.getEquipmentNumber())
                    .eq("ip", device.getIp())
                    .eq("state", 1)
                    .and(q -> q
                        .le("start_time", end)
                        .or()
                        .le("end_time", end)
                    )
            );
//            log.info("[掉线统计] 设备[{}]掉线记录数: {}", device.getEquipmentNumber(), offlineList.size());
            for (DeviceStatusHistory offline : offlineList) {
                LocalDateTime offlineStart = offline.getStartTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime offlineEnd = offline.getEndTime() != null 
                    ? offline.getEndTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() 
                    : LocalDateTime.now();
                // 只统计与排班时间重叠部分
                LocalDateTime overlapStart = offlineStart.isAfter(start) ? offlineStart : start;
                LocalDateTime overlapEnd = offlineEnd.isBefore(end) ? offlineEnd : end;
                if (overlapEnd.isAfter(overlapStart)) {
                    int minutes = (int) java.time.Duration.between(overlapStart, overlapEnd).toMinutes();
//                    log.info("[掉线统计] 设备[{}]掉线区间: {} ~ {}，与排班重叠: {} ~ {}，时长: {}分钟", device.getEquipmentNumber(), offlineStart, offlineEnd, overlapStart, overlapEnd, minutes);
                    totalCount++;
                    totalMinutes += minutes;
//                } else {
//                    log.info("[掉线统计] 设备[{}]掉线区间: {} ~ {}，与排班无重叠", device.getEquipmentNumber(), offlineStart, offlineEnd);
                }
            }
        }
        log.info("[掉线统计] 统计结果: 掉线次数={}, 掉线时长(分钟)={}", totalCount, totalMinutes);
        return new int[]{totalCount, totalMinutes};
    }
} 