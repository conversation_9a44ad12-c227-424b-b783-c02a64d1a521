package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.dto.SiteAttendanceResponseDTO;
import com.demo.entity.*;
import com.demo.entity.DTO.AssignACounselor;
import com.demo.entity.DTO.IocationDTO;
import com.demo.entity.DTO.PersuasionInDetail;
import com.demo.entity.DTO.SelectIllegalRecords;
import com.demo.enums.LeaveStatusEnum;
import com.demo.enums.PlateColorEnum;
import com.demo.enums.TrafficEnum;
import com.demo.enums.VehicleTypeEnum;
import com.demo.mapper.*;
import com.demo.service.IllegalRecordsService;
import com.demo.service.OvertimeService;
import com.demo.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.demo.config.RelatedConfigurations.relatedconfigurations;
import static com.demo.enums.HandleEnum.*;
import static com.demo.enums.PersuasionEnum.EFFECTIVE_PERSUASION;
import static java.util.concurrent.TimeUnit.HOURS;

/**
 * <AUTHOR>
 */
@Slf4j  // 添加 Lombok 日志注解
@Service
public class IllegalRecordsServiceImpl extends ServiceImpl<IllegalRecordsMapper, IllegalRecords> implements IllegalRecordsService {
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    IllegalRecordsMapper illegalRecordsMapper;
    @Autowired
    UsersMapper usersMapper;
    @Autowired
    AccuratePersuasionMapper accuratePersuasionMapper;
    @Autowired
    TreeNodeMapper treeNodeMapper;
    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    AlarmMapper alarmMapper;
    @Autowired
    PhoneNotifyClient phoneNotifyClient;
    @Autowired
    private LeavePostMapper leavePostMapper;
    @Autowired
    ScheduleMapper scheduleMapper;
    @Autowired
    ShiftMapper shiftMapper;
    @Autowired
    DeviceStatusHistoryMapper deviceStatusHistoryMapper;
    @Autowired
    private LeaveMapper leaveMapper;
    @Autowired
    private AsyncHttpClient asyncHttpClient;
    @Autowired
    private AttendanceMapper attendanceMapper;
    @Autowired
    private OvertimeService overtimeService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    @Qualifier("attendanceTaskExecutor")
    private Executor attendanceTaskExecutor;

    /**
     * 超员大于3.同一车牌大于三
     */
    @Override
    @Transactional // 添加事务注解
    public SaResult addIllegalRecords(IllegalRecords illegalRecords, MultipartFile[] files) throws  IOException {
        // 备份文件字节数组，避免流被消耗后无法重复读取
        MultipartFile[] backupFiles = null;
        if (files != null && files.length > 0) {
            log.info("=== 上传的原始文件信息 ===");
            backupFiles = new MultipartFile[files.length];
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                if (file != null && !file.isEmpty()) {
                    log.info("原始文件[{}]: 名称={}, 大小={} bytes, 类型={}",
                            i,
                            file.getOriginalFilename(),
                            file.getSize(),
                            file.getContentType());

                    // 立即备份文件字节，避免后续流被消耗
                    try {
                        byte[] fileBytes = file.getBytes();
                        backupFiles[i] = new Base64MultipartFile(fileBytes, file.getOriginalFilename());
                        log.info("文件[{}]字节备份成功，大小: {} bytes", i, fileBytes.length);
                    } catch (IOException e) {
                        log.error("文件[{}]字节备份失败: {}", i, e.getMessage());
                        backupFiles[i] = null;
                    }
                } else {
                    backupFiles[i] = null;
                }
            }
            log.info("========================");
        }
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", illegalRecords.getEquipmentNumber());
        queryWrapper.eq("device_type", "电脑");
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        if (devices == null || devices.isEmpty()) {
            return SaResult.error("设备不存在");
        }
        Device device = devices.get(0);
        //查询是否存在这个点位，不存在则创建
        String city = device.getCity();
        String county = device.getCounty();
        String township = device.getTownship();
        String hamlet = device.getHamlet();
        String site = device.getSite();
        TreeNode cityNode = insertIfNotExists(city, 0, 0);
        if (cityNode != null) {
            // 检查并插入县
            TreeNode countyNode = insertIfNotExists(county, cityNode.getId(), 1);
            if (countyNode != null) {
                // 检查并插入镇
                TreeNode townshipNode = insertIfNotExists(township, countyNode.getId(), 2);
                if (townshipNode != null) {
                    // 检查并插入村
                    TreeNode hamletNode = insertIfNotExists(hamlet, townshipNode.getId(), 3);
                    if (hamletNode != null) {
                        // 检查并插入详细地址
                        insertIfNotExists(site, hamletNode.getId(), 4);
                    }
                }
            }
        }
        illegalRecords.setUuid(IdUtil.simpleUUID());
        illegalRecords.setIllegalName(DateUtil.format(illegalRecords.getCaptureTime(), "yyyyMMddHHmmss") + "--" + illegalRecords.getIllegalType().getDesc());
        illegalRecords.setCity(city);
        illegalRecords.setCounty(county);
        illegalRecords.setTownship(township);
        illegalRecords.setHamlet(hamlet);
        illegalRecords.setSite(site);
        illegalRecords.setLongitude(device.getLongitude());
        illegalRecords.setLatitude(device.getLatitude());
        illegalRecords.setCreateTime(new Date());
        String formatDate = DateUtil.formatDate(illegalRecords.getCreateTime());    //日期
        String address = buildAddress(illegalRecords);    //地址
        String uuid = illegalRecords.getUuid();     //UUID
        String folderName = formatDate + "/" + address + "/" + uuid;    //构建文件夹名
        if (files != null && files.length != 0) {
            ArrayList<String> urls = new ArrayList<>();
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                String upload = minioUtil.uploadFile(file, fileName, folderName);
                urls.add(upload);
            }
            illegalRecords.setPictureUrl(String.join(",", urls));
        }
            illegalRecords.setDisposalStatus(NOT_REQUIRED);    //正常车辆设置0
            //illegalRecords.setHandMovement(NOT_MANUAL_ASSIGNMENT);
            illegalRecordsMapper.insert(illegalRecords);
        // 异步提交HTTP请求，不影响现有的返回
        // 使用备份的文件数组，避免原始文件流被MinIO消耗后无法读取
        if (backupFiles != null) {
            Map<String, Object> params = new HashMap<>();
            params.put("is_coming1", illegalRecords.getIsComing1());
            params.put("is_coming2", illegalRecords.getIsComing2());
            params.put("illegal_zone1", illegalRecords.getIllegalZone1());
            params.put("illegal_zone2", illegalRecords.getIllegalZone2());
            params.put("tipsText", illegalRecords.getTipsText());
            log.info("使用备份文件进行异步HTTP请求，备份文件数: {}", backupFiles.length);
            asyncHttpClient.postMultipartAsync("http://***************:5000/illegal_identify", params, backupFiles)
                .thenAccept(response -> {
                    log.info("异步请求成功");
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        // 假设 response 是 String 类型
                        if (response != null) {
                            // 将 JSON 字符串解析为 Map
                            Map<String, Object> resultMap = objectMapper.readValue(
                                    (String) response,
                                    new TypeReference<Map<String, Object>>() {}
                            );
                            // 现在你可以安全地操作 resultMap 了
                            if (resultMap.containsKey("error")) {
                                log.error("接口返回错误: {}", resultMap.get("error"));
                                return;
                            }
                            // 提取字段
                            String frame1 = getString(resultMap, "frame1");
                            String frame2 = getString(resultMap, "frame2");
                            Integer illegalType = getInteger(resultMap, "illegalType");
                            Integer numberOfPassengers = getInteger(resultMap, "numberOfPassengers");
                            numberOfPassengers = (numberOfPassengers == null) ? 0 : numberOfPassengers; //替换后的人数
                            Integer plateColor = getInteger(resultMap, "plateColor");
                            String plateNumber = getString(resultMap, "plateNumber");
                            Integer vehicleType = getInteger(resultMap, "vehicleType");
                            String fileName1 = getString(resultMap, "file_name1");
                            String fileName2 = getString(resultMap, "file_name2");
                            if (vehicleType != null) {
                                illegalRecords.setVehicleType(VehicleTypeEnum.of(vehicleType));
                            }
                            if (plateColor != null) {
                                illegalRecords.setPlateColor(PlateColorEnum.of(plateColor));
                            }
                            if (plateNumber != null) {
                                illegalRecords.setPlateNumber(plateNumber);
                                // 检查车牌历史违法记录，决定是否自动下派
                                checkPlateHistoryAndAutoDispatch(illegalRecords, plateNumber);
                            }
                            if (illegalType != null) {
                                illegalRecords.setIllegalType(TrafficEnum.of(illegalType));
                            } else {
                                // illegalType为null时，删除这条违法记录
                                log.info("违法类型为null，删除违法记录ID: {}", illegalRecords.getUuid());
                                deleteIllegalRecordWithRelatedData(illegalRecords.getUuid());
                                return; // 提前返回，不再执行后续的更新操作
                            }
                            // 修改条件：只要新识别的人数不为null且不为0就更新
                            if (numberOfPassengers != 0) {
                                illegalRecords.setNumberOfPassengers(numberOfPassengers);
                            }
                            if (PlateColorEnum.BLUE.equals(illegalRecords.getPlateColor())&&!VehicleTypeEnum.NOT_OVERLOAD.equals(illegalRecords.getVehicleType())) {
                                illegalRecords.setVehicleType(VehicleTypeEnum.BATTERY_CART);
                            }
//                            ArrayList<String> urls = new ArrayList<>();
                            if (frame1 != null && !frame1.isEmpty()) {
                                Base64.Decoder decoder = Base64.getDecoder();
                                byte[] frame1Bytes = decoder.decode(frame1);
                                MultipartFile frame1File = new Base64MultipartFile(frame1Bytes, fileName1);
                                minioUtil.uploadFile(frame1File, fileName1, folderName);
//                                urls.add(url1);
                            }
                            if (frame2 != null && !frame2.isEmpty()) {
                                Base64.Decoder decoder = Base64.getDecoder();
                                byte[] frame2Bytes = decoder.decode(frame2);
                                MultipartFile frame2File = new Base64MultipartFile(frame2Bytes, fileName2);
                                minioUtil.uploadFile(frame2File, fileName2, folderName);
//                                urls.add(url2);
                            }
//                            illegalRecords.setActualPictureUrl(String.join(",", urls));
                            //如果识别人数大于等于配置的过载人数，则自动下派和报警
                            List<Users> users;
                            if (illegalRecords.getNumberOfPassengers() >= relatedconfigurations.getOverload()) {
                                //查询下派劝导员
                                users = usersMapper.selectProselytizer(illegalRecords.getCity(), illegalRecords.getCounty(),
                                        illegalRecords.getTownship(), illegalRecords.getHamlet(), illegalRecords.getSite());
                                if (users.isEmpty()) {
//                log.error("没有找到对应点位的劝导员");
                                    //illegalRecords.setHandMovement(MANUAL_ASSIGNMENT);
                                    users = usersMapper.selectProselytizer(illegalRecords.getCity(), illegalRecords.getCounty(),
                                            illegalRecords.getTownship(), illegalRecords.getHamlet(), "");
                                    if (users.isEmpty()) {
//                    log.error("没有找到对应村的劝导员");
                                        illegalRecords.setDisposalStatus(UNTREATED);
                                    } else {
                                        //下派违法
                                        downcast(users, illegalRecords);
                                        illegalRecords.setDisposalStatus(SENT_DOWN);
                                    }
                                } else {
                                    //illegalRecords.setHandMovement(ASSIGNED);
                                    //下派违法
                                    downcast(users, illegalRecords);
                                    illegalRecords.setDisposalStatus(SENT_DOWN);
                                }
                                saveTheAlarm(illegalRecords);
                            }
                            illegalRecordsMapper.updateById(illegalRecords);
                        } else {
                            log.info("响应类型不是字符串: {}", response.getClass());
                        }
                    } catch (Exception e) {
                        log.error("JSON 解析失败: {}", e.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("异步请求失败: {}", throwable.getMessage());
                    return null;
                });
        } else {
            log.info("备份文件为空，跳过异步HTTP请求");
        }
        return SaResult.ok("上传成功");
    }
    /**
     * 下派违法
     * @param users 劝导员
     * @param illegalRecords 违法记录
     * @throws IOException
     */
    private void downcast(List<Users> users, IllegalRecords illegalRecords) throws IOException {
        for (Users user : users) {
            AccuratePersuasion accuratePersuasion = new AccuratePersuasion();
            accuratePersuasion.setIllegalName(illegalRecords.getIllegalName());
            accuratePersuasion.setUuid(IdUtil.simpleUUID());
            accuratePersuasion.setIllegalRecordsUuid(illegalRecords.getUuid());
            accuratePersuasion.setUserId(user.getUserId());
            accuratePersuasion.setUserName(user.getName());
            accuratePersuasion.setDisposalStatus(0);
            accuratePersuasion.setCity(illegalRecords.getCity());
            accuratePersuasion.setCounty(illegalRecords.getCounty());
            accuratePersuasion.setTownship(illegalRecords.getTownship());
            accuratePersuasion.setHamlet(illegalRecords.getHamlet());
            accuratePersuasion.setSite(illegalRecords.getSite());
            Date date = new Date();
            Date newDate = DateUtil.offset(date, DateField.DAY_OF_MONTH, 5);
            accuratePersuasion.setDeadlineTime(newDate);    //截止处理时间
            accuratePersuasion.setLowerFactionType("自动下派");
            accuratePersuasionMapper.insert(accuratePersuasion);
            //打电话
            if (relatedconfigurations.getPhone()) {
                phoneNotifyClient.sendPhoneNotify("收到违法下派", "15388336114");
            }
        }
    }
    /**
     * 保存报警
     * @param illegalRecords 违法记录
     */
    private void saveTheAlarm(IllegalRecords illegalRecords) {
        String locations = this.concatenateLocation(illegalRecords.getCity(), illegalRecords.getCounty(), illegalRecords.getTownship(), illegalRecords.getHamlet(), illegalRecords.getSite());
        Alarm alarm = new Alarm();
        alarm.setAlarmType(illegalRecords.getIllegalType().getDesc());
        alarm.setContent(locations + "发生了违法");
        alarm.setTargetType("交通违法");
        if (illegalRecords.getNumberOfPassengers() > relatedconfigurations.getOverload()) {
            alarm.setDegree("严重");
        } else if (illegalRecords.getIllegalType().getCode() > 3) {
            alarm.setDegree("一般");
        } else {
            alarm.setDegree("轻微");
        }
        alarm.setWfUuid(illegalRecords.getUuid());
        alarm.setEventTime(illegalRecords.getCaptureTime());
        alarm.setCity(illegalRecords.getCity());
        alarm.setCounty(illegalRecords.getCounty());
        alarm.setTownship(illegalRecords.getTownship());
        alarm.setHamlet(illegalRecords.getHamlet());
        alarm.setSite(illegalRecords.getSite());
        alarmMapper.insert(alarm);
    }

    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value == null ? null : value.toString();
    }

    private Integer getInteger(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException ignored) {
            }
        }
        return null;
    }

    /**
     * 构建地址
     */
    private String buildAddress(IllegalRecords object) {
        StringBuilder address = new StringBuilder(object.getCity());
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(object.getCounty())) {
            address.append(object.getCounty());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(object.getTownship())) {
            address.append(object.getTownship());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(object.getHamlet())) {
            address.append(object.getHamlet());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(object.getSite())) {
            address.append(object.getSite());
        }
        return address.toString();
    }

    @Override
    public SaResult addVideo(MultipartFile[] files, Date parse, Integer vehicleId, String cameraName, String equipmentNumber) {
        QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("vehicle_id", vehicleId);
        queryWrapper.eq("restart_time", parse);
        queryWrapper.eq("camera_name", cameraName);
        queryWrapper.eq("equipment_number", equipmentNumber);
        IllegalRecords illegalRecords;
        illegalRecords = illegalRecordsMapper.selectOne(queryWrapper);
        if (illegalRecords == null) {
            QueryWrapper<IllegalRecords> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("vehicle_id_two", vehicleId);
            queryWrapper1.eq("restart_time", parse);
            queryWrapper1.eq("camera_name_two", cameraName);
            queryWrapper1.eq("equipment_number", equipmentNumber);
            illegalRecords = illegalRecordsMapper.selectOne(queryWrapper1);
            if (illegalRecords == null) {
                return SaResult.error("未找到对应的违法记录");
            }
        }
        if (files == null || files.length == 0) {
            return SaResult.error("未上传任何视频文件");
        }
        MultipartFile file = files[0];
        long durationBackString = VideoUtil.getDurationBackMillis(file);
        String formatDate = DateUtil.formatDate(illegalRecords.getCreateTime());    //日期
        String address = buildAddress(illegalRecords);    //地址
        String uuid = illegalRecords.getUuid();     //UUID
        String folderName = formatDate + "/" + address + "/" + uuid;    //构建文件夹名
        String upload = minioUtil.uploadFile(file, file.getOriginalFilename(), folderName);
        illegalRecords.setVideoUrl(upload);
        if (durationBackString > relatedconfigurations.getEffectivePersuasion()) {
            illegalRecords.setPersuasiveBehavior(EFFECTIVE_PERSUASION);
        }
        illegalRecordsMapper.updateById(illegalRecords);
        return SaResult.ok("上传成功");
    }

    /**
     * 获取字符串最后一个数字
     */
    public static String getLastNumber(String input) {
        // 正则表达式匹配数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(input);

        // 用列表保存所有找到的数字
        List<String> numbers = new ArrayList<>();
        while (matcher.find()) {
            numbers.add(matcher.group());
        }
        // 返回找到的最后一个数字
        if (!numbers.isEmpty()) {
            return numbers.get(numbers.size() - 1);
        } else {
            return null;
        }
    }

    /**
     * 获取字符串第一个数字
     */
    public static String getFirstNumber(String input) {
        // 先用 / 截取字符串，获取最后一段的内容
        String[] parts = input.split("/");
        String lastPart = parts[parts.length - 1];
        // 正则表达式匹配数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(lastPart);
        // 查找第一个匹配的数字
        if (matcher.find()) {
            return matcher.group();
        }
        // 如果没有找到任何数字，返回null或其它你认为合适的值
        return null;
    }

    @Override
    public SaResult addPlateNumber(String plateNumber, DateTime parse, Integer vehicleId, String equipmentNumber, String cameraName,
                                   Integer plateColor, MultipartFile[] files, MultipartFile[] framePath, Boolean isComing1, Boolean isComing2) throws IOException {
        return SaResult.ok();
//        QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("vehicle_id", vehicleId);
//        queryWrapper.eq("restart_time", parse);
//        queryWrapper.eq("camera_name", cameraName);
//        queryWrapper.eq("equipment_number", equipmentNumber);
//        IllegalRecords illegalRecords;
//        illegalRecords = illegalRecordsMapper.selectOne(queryWrapper);
//        if (illegalRecords == null) {
//            QueryWrapper<IllegalRecords> queryWrapper1 = new QueryWrapper<>();
//            queryWrapper1.eq("vehicle_id_two", vehicleId);
//            queryWrapper1.eq("restart_time", parse);
//            queryWrapper1.eq("camera_name_two", cameraName);
//            queryWrapper1.eq("equipment_number", equipmentNumber);
//            illegalRecords = illegalRecordsMapper.selectOne(queryWrapper1);
//            if (illegalRecords == null) {
////                log.info("未找到对应的违法记录");
//                return SaResult.error("未找到对应的违法记录");
//            }
//        }
//        String pictureUrl = illegalRecords.getPictureUrl();
//        String formatDate = DateUtil.formatDate(illegalRecords.getCreateTime());    //日期
//        String address = buildAddress(illegalRecords);    //地址
//        String uuid = illegalRecords.getUuid();     //UUID
//        String folderName = formatDate + "/" + address + "/" + uuid;    //构建文件夹名
//        if (pictureUrl != null && framePath != null && framePath.length != 0) {
////            log.info("大图图片名字:{}", framePath[0].getOriginalFilename());
//            String[] array = pictureUrl.split(",");
//            String lastNumber = getLastNumber(cameraName);
//            if ("1".equals(lastNumber) && isComing1 != null && !isComing1) {
//                for (int i = 0; i < array.length; i++) {
//                    String s = array[i];
//                    String firstNumber = getFirstNumber(s);
//                    if ("1".equals(firstNumber)) {
//                        String fileName = framePath[0].getOriginalFilename();
//                        String upload = minioUtil.uploadFile(framePath[0], fileName, folderName);
////                        log.info("修改图片地址：{}修改为{}", s, upload);
//                        array[i] = upload; // 更新数组中的元素
//                        break; // 如果需要在这里退出循环
//                    }
//                }
//            } else {
//                for (int i = 0; i < array.length; i++) {
//                    String s = array[i];
//                    String firstNumber = getFirstNumber(s);
//                    if ("2".equals(firstNumber) && isComing2 != null && !isComing2) {
//                        String fileName = framePath[0].getOriginalFilename();
//                        String upload = minioUtil.uploadFile(framePath[0], fileName, folderName);
////                        log.info("修改图片地址：{}修改为{}", s, upload);
//                        array[i] = upload; // 更新数组中的元素
//                        break; // 如果需要在这里退出循环
//                    }
//                }
//            }
////            log.info("修改前的图片地址：{}", illegalRecords.getPictureUrl());
//            illegalRecords.setPictureUrl(String.join(",", array));
////            log.info("修改后的图片地址：{}", illegalRecords.getPictureUrl());
//        }
//        // 调用外部接口获取车牌号
//        if (plateColor != null && files != null && files.length != 0) {
//            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
//            body.add("plateColor", plateColor.toString());
//            for (MultipartFile file : files) {
//                body.add("files", new ByteArrayResource(Objects.requireNonNull(file.getBytes())) {
//                    @Override
//                    public String getFilename() {
//                        return file.getOriginalFilename();
//                    }
//                });
//            }
////            illegalRecords.setPlateNumber(recognizePlateNumber(body, plateNumber));
//            String updatePlateNumber = recognizePlateNumber(body, plateNumber);
//            if (StringUtils.isNotBlank(updatePlateNumber)) {
//                illegalRecords.setUpdatePlateNumber(updatePlateNumber);
//            }
//        }
//        if (plateColor != null) {
//            illegalRecords.setPlateColor(PlateColorEnum.of(plateColor));
//        }
//        if (files != null && files.length != 0) {
//            ArrayList<String> urls = new ArrayList<>();
//            for (MultipartFile file : files) {
//                String fileName = file.getOriginalFilename();
//                String upload = minioUtil.uploadFile(file, fileName, folderName);
//                urls.add(upload);
//            }
//            String existingUrl = illegalRecords.getNumberplateUrl();
//            String newUrls = String.join(",", urls);
//            if (existingUrl != null && !existingUrl.isEmpty()) {
//                illegalRecords.setNumberplateUrl(existingUrl + "," + newUrls);
//            } else {
//                illegalRecords.setNumberplateUrl(newUrls);
//            }
//        }
//        illegalRecords.setWhetherToModify(1);
//        illegalRecordsMapper.updateById(illegalRecords);
//        return SaResult.ok("延时修改车牌成功");
    }

    @Override
    public SaResult getAttendance(String city, String county, String township,
                                  String hamlet, String site, LocalDate date) {
        try {
            // 查询考勤记录
            List<Map<String, Object>> attendanceList = usersMapper.getAttendanceUser(
                    city, county, township, hamlet, site, date);

            // 查询脱岗记录
            List<LeavePost> leavePosts = leavePostMapper.getLeavePostsByDate(
                    city, county, township, hamlet, site, date);

            // 按点位分组
            Map<String, List<Map<String, Object>>> siteGroups = attendanceList.stream()
                    .collect(Collectors.groupingBy(record -> {
                        return String.format("%s-%s-%s-%s-%s",
                                record.get("city"),
                                record.get("county"),
                                record.get("township"),
                                record.get("hamlet"),
                                record.get("site"));
                    }));

            // 创建返回对象
            SiteAttendanceResponseDTO response = new SiteAttendanceResponseDTO();
            response.setDate(date);
            response.setSites(new ArrayList<>());
            response.setStatistics(new SiteAttendanceResponseDTO.StatisticsDTO());
            response.getStatistics().setStatusCount(new SiteAttendanceResponseDTO.StatusCountDTO());

            // 处理每个点位的数据
            for (Map.Entry<String, List<Map<String, Object>>> entry : siteGroups.entrySet()) {
                String[] locationParts = entry.getKey().split("-");
                List<Map<String, Object>> siteRecords = entry.getValue();

                SiteAttendanceResponseDTO.SiteDTO siteDTO = new SiteAttendanceResponseDTO.SiteDTO();
                siteDTO.setSiteId(locationParts[4]); // 使用site作为siteId

                // 设置位置信息
                SiteAttendanceResponseDTO.LocationDTO location = new SiteAttendanceResponseDTO.LocationDTO();
                location.setCity(locationParts[0]);
                location.setCounty(locationParts[1]);
                location.setTownship(locationParts[2]);
                location.setHamlet(locationParts[3]);
                location.setSite(locationParts[4]);
                siteDTO.setLocation(location);
                // 处理人员信息（包括无排班的人员）
                Map<Integer, SiteAttendanceResponseDTO.StaffDTO> staffMap = new HashMap<>();
                for (Map<String, Object> record : siteRecords) {
                    Integer userId = (Integer) record.get("user_id");
                    SiteAttendanceResponseDTO.StaffDTO staffDTO = staffMap.computeIfAbsent(userId, k -> {
                        SiteAttendanceResponseDTO.StaffDTO newStaff = new SiteAttendanceResponseDTO.StaffDTO();
                        newStaff.setUserId(userId);
                        newStaff.setUserName((String) record.get("name"));
                        newStaff.setPhone((String) record.get("phone"));
                        newStaff.setDeptName((String) record.get("dept_name"));
                        newStaff.setShifts(new ArrayList<>());
                        return newStaff;
                    });
                    // 判断是否有班次
                    if (record.get("schedule_id") == null) {
                        // 没有排班，shifts为空，状态为休息
                        staffDTO.setStatus("未排班");
                    }
                }
                // 处理班次信息
                List<SiteAttendanceResponseDTO.ShiftDTO> shifts = new ArrayList<>();
                Map<String, List<Map<String, Object>>> shiftGroups = siteRecords.stream()
                        .collect(Collectors.groupingBy(record ->
                                (String) Optional.ofNullable(record.get("shift_name")).orElse("未排班")));
                for (Map.Entry<String, List<Map<String, Object>>> shiftEntry : shiftGroups.entrySet()) {
                    String shiftName = shiftEntry.getKey();
                    if (shiftName == null || "未排班".equals(shiftName)) {
                        // 没有班次的情况，直接跳过，不生成班次时间等信息
                        continue;
                    }
                    SiteAttendanceResponseDTO.ShiftDTO shiftDTO = new SiteAttendanceResponseDTO.ShiftDTO();
                    shiftDTO.setShiftName(shiftName);
                    shiftDTO.setStaffCount(shiftEntry.getValue().size());
                    // 设置班次时间
                    Map<String, Object> firstRecord = shiftEntry.getValue().get(0);
                    shiftDTO.setStartTime((String) firstRecord.get("start_time"));
                    shiftDTO.setEndTime((String) firstRecord.get("end_time"));
                    shifts.add(shiftDTO);
                }
                siteDTO.setShifts(shifts);

                // 获取该点位的设备信息
                QueryWrapper<Device> deviceQuery = new QueryWrapper<>();
                deviceQuery.eq("city", locationParts[0]);
                deviceQuery.eq("county", locationParts[1]);
                deviceQuery.eq("township", locationParts[2]);
                deviceQuery.eq("hamlet", locationParts[3]);
                deviceQuery.eq("site", locationParts[4]);
                List<Device> devices = deviceMapper.selectList(deviceQuery);

                // 查询当天的设备状态历史记录（包括已结束和未结束的）
                List<DeviceStatusHistory> deviceStatusHistories = new ArrayList<>();
                if (!devices.isEmpty()) {
                    // 设置当天的时间范围
                    LocalDateTime startOfDay = date.atStartOfDay();
                    LocalDateTime endOfDay = date.atTime(23, 59, 59);
                    // 先查询所有未结束的离线记录
                    List<DeviceStatusHistory> ongoingOfflineRecords = deviceStatusHistoryMapper.selectList(
                        new QueryWrapper<DeviceStatusHistory>()
                            .in("equipment_number", devices.stream()
                                .map(Device::getEquipmentNumber)
                                .collect(Collectors.toList()))
                            .in("ip", devices.stream()
                                .map(Device::getIp)
                                .collect(Collectors.toList()))
                            .isNull("end_time")
                            .eq("state", 1) // 状态1表示离线/故障
                    );
                    // 再查询当天的离线记录
                    List<DeviceStatusHistory> todayOfflineRecords = deviceStatusHistoryMapper.selectList(
                        new QueryWrapper<DeviceStatusHistory>()
                            .in("equipment_number", devices.stream()
                                .map(Device::getEquipmentNumber)
                                .collect(Collectors.toList()))
                            .in("ip", devices.stream()
                                .map(Device::getIp)
                                .collect(Collectors.toList()))
                            .and(wrapper -> wrapper
                                // 开始时间在当天范围内
                                .between("start_time", startOfDay, endOfDay)
                                .or()
                                // 或者结束时间在当天范围内
                                .between("end_time", startOfDay, endOfDay)
                            )
                            .eq("state", 1) // 状态1表示离线/故障
                            .orderByDesc("start_time")
                    );
                    
                    // 合并两种记录，去重
                    deviceStatusHistories = Stream.concat(ongoingOfflineRecords.stream(), todayOfflineRecords.stream())
                        .distinct()
                        .collect(Collectors.toList());
                }

                // 检查设备在线状态（只检查当前未结束的故障）
                List<DeviceStatusHistory> currentOfflineStatuses = deviceStatusHistories.stream()
                    .filter(s -> s.getEndTime() == null)
                    .collect(Collectors.toList());
                
                boolean isDeviceOnline = devices.isEmpty() || currentOfflineStatuses.isEmpty();
                
                // 初始化设备故障记录列表
                List<SiteAttendanceResponseDTO.DeviceFaultRecordDTO> deviceFaultRecords = new ArrayList<>();
                
                // 处理当天的所有设备故障记录
                if (!devices.isEmpty()) {
                    for (Device device : devices) {
                        // 查找该设备当天的所有故障记录
                        List<DeviceStatusHistory> deviceFaults = deviceStatusHistories.stream()
                            .filter(s -> s.getEquipmentNumber().equals(device.getEquipmentNumber())
                                && s.getIp().equals(device.getIp()))
                            .collect(Collectors.toList());
                        
                        // 为每条故障记录创建故障记录DTO
                        for (DeviceStatusHistory faultHistory : deviceFaults) {
                            SiteAttendanceResponseDTO.DeviceFaultRecordDTO faultRecord = new SiteAttendanceResponseDTO.DeviceFaultRecordDTO();
                            faultRecord.setEquipmentNumber(device.getEquipmentNumber());
                            faultRecord.setIp(device.getIp());
                            faultRecord.setStartTime(formatDateTime(faultHistory.getStartTime().toString()));
                            
                            if (faultHistory.getEndTime() != null) {
                                faultRecord.setEndTime(formatDateTime(faultHistory.getEndTime().toString()));
                                // 计算故障持续时间
                                long durationMinutes = DateUtil.between(faultHistory.getStartTime(), faultHistory.getEndTime(), DateUnit.SECOND);
                                faultRecord.setDuration(durationMinutes + "秒钟");
                            } else {
                                faultRecord.setEndTime("至今");
                                faultRecord.setDuration("进行中");
                            }
                            
                            faultRecord.setDescription("设备离线");
                            deviceFaultRecords.add(faultRecord);
                        }
                        
                        // 如果没有找到故障记录，但设备当前确实离线，创建一个当前状态记录
                        if (deviceFaults.isEmpty() && !isDeviceOnline) {
                            SiteAttendanceResponseDTO.DeviceFaultRecordDTO faultRecord = new SiteAttendanceResponseDTO.DeviceFaultRecordDTO();
                            faultRecord.setEquipmentNumber(device.getEquipmentNumber());
                            faultRecord.setIp(device.getIp());
                            faultRecord.setStartTime(formatDateTime(LocalDateTime.now().toString()));
                            faultRecord.setEndTime("至今");
                            faultRecord.setDuration("进行中");
                            faultRecord.setDescription("设备离线");
                            deviceFaultRecords.add(faultRecord);
                        }
                    }
                }
                
                // 添加设备故障记录到点位DTO
                siteDTO.setDeviceFaultRecords(deviceFaultRecords);

                // 处理人员信息
                boolean hasAbnormalStaff = false;       //是否有脱岗记录
                boolean isInScheduleTime = false;       //是否在排版时间内
                boolean hasLeaveRecord = false;         //是否请假
                // 先处理所有脱岗记录，但不在这里设置 hasAbnormalStaff
                Map<Integer, Map<String, List<SiteAttendanceResponseDTO.AbnormalRecordDTO>>> userShiftAbnormalRecords = new HashMap<>();
                for (LeavePost leavePost : leavePosts) {
                    if (leavePost.getSite().equals(locationParts[4])) {
                        Integer userId = leavePost.getUserId();
                        
                        // 获取班次信息
                        Schedule schedule = scheduleMapper.selectById(leavePost.getScheduleId());
                        if (schedule != null) {
                            Shift shift = shiftMapper.selectById(schedule.getShiftId());
                            if (shift != null) {
                                // 添加脱岗记录
                                String shiftName = shift.getStartTime().getHour() < 12 ? "早" : "晚";
                                SiteAttendanceResponseDTO.AbnormalRecordDTO abnormalRecord = new SiteAttendanceResponseDTO.AbnormalRecordDTO();                                
                                abnormalRecord.setType("脱岗");                                
                                abnormalRecord.setStartTime(formatDateTime(leavePost.getStartTime().toString()));                                
                                abnormalRecord.setEndTime(leavePost.getEndTime() != null ? formatDateTime(leavePost.getEndTime().toString()) : "至今");                                
                                abnormalRecord.setDuration(leavePost.getDuration() != null ? leavePost.getDuration() + "分钟" : "进行中");                                
                                abnormalRecord.setDescription("未在岗");
                                
                                // 将脱岗记录添加到用户对应班次的异常记录中
                                userShiftAbnormalRecords
                                    .computeIfAbsent(userId, k -> new HashMap<>())
                                    .computeIfAbsent(shiftName, k -> new ArrayList<>())
                                    .add(abnormalRecord);
                                
                                // 注意：这里不再设置 hasAbnormalStaff = true，而是在后面处理具体用户状态时判断
                            }
                        }
                    }
                }

                // 定义状态优先级
                Map<String, Integer> statusPriority = new HashMap<>();
                statusPriority.put("故障", 3);
                statusPriority.put("在岗", 2);
                statusPriority.put("脱岗", 1);
                statusPriority.put("休息", 0);

                for (Map<String, Object> record : siteRecords) {
                    Integer userId = (Integer) record.get("user_id");
                    String shiftName = (String) record.get("shift_name");
                    
                    // 获取或创建人员记录
                    SiteAttendanceResponseDTO.StaffDTO staffDTO = staffMap.computeIfAbsent(userId, k -> {
                        SiteAttendanceResponseDTO.StaffDTO newStaff = new SiteAttendanceResponseDTO.StaffDTO();
                        newStaff.setUserId(userId);
                        newStaff.setUserName((String) record.get("name"));
                        newStaff.setPhone((String) record.get("phone"));
                        newStaff.setDeptName((String) record.get("dept_name"));
                        newStaff.setShifts(new ArrayList<>());
                        return newStaff;
                    });
                    if (shiftName == null || "未排班".equals(shiftName)) {
                        // 没有班次，shifts为空，状态为休息
                        staffDTO.setStatus("休息");
                        // 不需要再做任何班次、异常、请假等处理
                        continue; // 直接跳过本次循环
                    }
                    // 用于记录每个用户所有班次的最高优先级状态
                    if (staffDTO.getStatus() == null) {
                        staffDTO.setStatus("休息"); // 默认休息
                    }
                    int currentPriority = statusPriority.getOrDefault(staffDTO.getStatus(), 0);
                    String finalStatus = staffDTO.getStatus();

                    // 创建新的班次记录
                    SiteAttendanceResponseDTO.StaffShiftDTO shiftDTO = new SiteAttendanceResponseDTO.StaffShiftDTO();
                    shiftDTO.setShiftName(shiftName);
                    shiftDTO.setStartTime((String) record.get("start_time"));
                    shiftDTO.setEndTime((String) record.get("end_time"));
                    shiftDTO.setAbnormalRecords(new ArrayList<>());
                    
                    // 获取该用户的排班信息
                    List<Schedule> userSchedules = scheduleMapper.selectSchedulesByDateAndLocation(
                        date,
                        locationParts[0],
                        locationParts[1],
                        locationParts[2],
                        locationParts[3],
                        locationParts[4]
                    ).stream()
                    .filter(schedule -> schedule.getUserId().equals(userId))
                    .collect(Collectors.toList());
                    
                    // 创建地点信息Map
                    Map<String, String> locationInfo = new HashMap<>();
                    locationInfo.put("city", locationParts[0]);
                    locationInfo.put("county", locationParts[1]);
                    locationInfo.put("township", locationParts[2]);
                    locationInfo.put("hamlet", locationParts[3]);
                    locationInfo.put("site", locationParts[4]);
                    
                    // 时间段
                    String timeSlot = "早".equals(shiftName) ? "am" : "pm";
                    // 判断人员状态
                    List<Map<String, Object>> abnormalRecords = new ArrayList<>();
                    Map<String, Object> statusResult = determineStaffStatus(
                        userId,
                        userSchedules,
                        isDeviceOnline,
                        locationInfo,
                        timeSlot,
                        abnormalRecords, staffDTO.getDeptName()
                    );
                    
                    // 更新班次状态
                    shiftDTO.setStatus((String) statusResult.get("status"));
                    
                    // 记录最高优先级状态
                    String shiftStatus = (String) statusResult.get("status");
                    int shiftPriority = statusPriority.getOrDefault(shiftStatus, 0);
                    if (shiftPriority > currentPriority) {
                        currentPriority = shiftPriority;
                        finalStatus = shiftStatus;
                    }
                    
                    // 统计标志
                    if ((boolean) statusResult.get("isAbnormal")) {
                        hasAbnormalStaff = true;
                    }
                    if ((boolean) statusResult.get("isInScheduleTime")) {
                        isInScheduleTime = true;
                    }
                    if ((boolean) statusResult.get("hasLeaveRecord")) {
                        hasLeaveRecord = true;
                    }
                    
                    // 添加异常记录（排除设备故障记录）
                    for (Map<String, Object> abnormalRecord : abnormalRecords) {
                        // 只添加非设备故障的异常记录
                        if (!"故障".equals(abnormalRecord.get("type"))) {
                            SiteAttendanceResponseDTO.AbnormalRecordDTO recordDTO = new SiteAttendanceResponseDTO.AbnormalRecordDTO();
                            recordDTO.setType((String) abnormalRecord.get("type"));
                            recordDTO.setStartTime(formatDateTime((String) abnormalRecord.get("startTime")));
                            recordDTO.setEndTime(formatDateTime((String) abnormalRecord.get("endTime")));
                            recordDTO.setDuration((String) abnormalRecord.get("duration"));
                            recordDTO.setDescription((String) abnormalRecord.get("description"));
                            shiftDTO.getAbnormalRecords().add(recordDTO);
                        }
                    }
                    
                    // 添加之前处理的脱岗记录
                    if (userShiftAbnormalRecords.containsKey(userId) && 
                        userShiftAbnormalRecords.get(userId).containsKey(shiftName)) {
                        shiftDTO.getAbnormalRecords().addAll(
                            userShiftAbnormalRecords.get(userId).get(shiftName)
                        );
                    }
                    // 添加班次记录到员工记录中
                    staffDTO.getShifts().add(shiftDTO);
                    // 循环结束后统一设置状态
                    staffDTO.setStatus(finalStatus);
                }

                // 将Map转换为List
                List<SiteAttendanceResponseDTO.StaffDTO> staffList = new ArrayList<>(staffMap.values());
                siteDTO.setStaff(staffList);
                // 构建点位唯一标识，使用下划线作为分隔符
                String locationKey = String.format("%s_%s_%s_%s_%s",
                        location.getCity(),
                        location.getCounty(),
                        location.getTownship(),
                        location.getHamlet(),
                        location.getSite());
                // 获取当前时间判断是上午还是下午
                String timeSlot = LocalTime.now().getHour() < 12 ? "am" : "pm";
                // 保存点位状态到Redis，区分上午和下午
                String redisKey = "site_status:" + locationKey + ":" + timeSlot;
                // 设置点位状态 - 保持状态判断逻辑不变
                siteDTO.setStatus(determineSiteStatus(isDeviceOnline, hasAbnormalStaff, isInScheduleTime,hasLeaveRecord,redisKey));
                
                response.getSites().add(siteDTO);
            }

            // 计算统计信息
            response.getStatistics().setTotalSites(response.getSites().size());
            Map<String, Long> statusCounts = response.getSites().stream()
                    .collect(Collectors.groupingBy(SiteAttendanceResponseDTO.SiteDTO::getStatus, Collectors.counting()));
            
            response.getStatistics().getStatusCount().setOnDuty(statusCounts.getOrDefault("在岗", 0L).intValue());
            response.getStatistics().getStatusCount().setOffDuty(statusCounts.getOrDefault("脱岗", 0L).intValue());
            response.getStatistics().getStatusCount().setRest(statusCounts.getOrDefault("休息", 0L).intValue());
            response.getStatistics().getStatusCount().setFault(statusCounts.getOrDefault("故障", 0L).intValue());

            return SaResult.data(response);
        } catch (Exception e) {
            log.error("查询考勤数据失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public SaResult getAttendanceAndWorkTimeByday(String city, String county, String township, String hamlet, String site, LocalDate date) {
        try {
            String groupByLevel = getTheNextLevel(city, county, township, hamlet, site);
            LocalDate today = LocalDate.now();
            boolean isToday = date.equals(today);

            if (isToday) {
                // 当天数据：实时计算，不缓存
                Map<String, Object> response = calculateDailyAttendanceData(city, county, township, hamlet, site, date, groupByLevel);
                return SaResult.data(response);
            } else {
                // 历史数据：使用缓存策略
                return getHistoricalAttendanceData(city, county, township, hamlet, site, date, groupByLevel);
            }

        } catch (Exception e) {
            log.error("查询考勤和工作时间统计失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取历史考勤数据（带缓存）
     */
    private SaResult getHistoricalAttendanceData(String city, String county, String township, String hamlet, String site, LocalDate date, String groupByLevel) {
        // 1. 尝试从Redis获取完整结果
        String resultCacheKey = buildCacheKey("attendance_result", city, county, township, hamlet, site, date, groupByLevel);
        String cachedResult = redisUtils.get(resultCacheKey);

        if (cachedResult != null) {
            try {
                Map<String, Object> response = objectMapper.readValue(cachedResult, Map.class);
                log.info("缓存命中: {}", resultCacheKey);
                return SaResult.data(response);
            } catch (Exception e) {
                log.info("缓存数据解析失败: {}", e.getMessage());
                redisUtils.delete(resultCacheKey); // 删除损坏的缓存
            }
        }

        // 2. 缓存未命中，计算数据
        Map<String, Object> response = calculateDailyAttendanceData(city, county, township, hamlet, site, date, groupByLevel);

        // 3. 存储完整结果到缓存
        try {
            String cacheValue = objectMapper.writeValueAsString(response);
            CacheExpiration expiration = calculateCacheExpiration(date);
            redisUtils.setEx(resultCacheKey, cacheValue, expiration.timeout, expiration.unit);
            log.info("缓存存储: {} (过期时间: {} {})", resultCacheKey, expiration.timeout, expiration.unit);
        } catch (Exception e) {
            log.info("存储缓存失败: {}", e.getMessage());
        }

        return SaResult.data(response);
    }

    /**
     * 计算日度考勤数据（先按站点计算，再汇总）
     */
    private Map<String, Object> calculateDailyAttendanceData(String city, String county, String township, String hamlet, String site, LocalDate date, String groupByLevel) {
        // 1. 先获取该区域下所有站点的数据
        List<Map<String, Object>> allSiteData = calculateAllSiteData(city, county, township, hamlet, site, date);

        // 2. 查询考勤统计数据（用于获取地域分组信息）
        List<Map<String, Object>> attendanceStats = attendanceMapper.getAttendanceStatsByArea(
                city, county, township, hamlet, site, date, groupByLevel);

        // 3. 按查询层级汇总站点数据
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> stat : attendanceStats) {
            String areaName = (String) stat.get("area_name");

            // 汇总该地域下所有站点的数据
            Map<String, Object> result = aggregateSiteDataByArea(allSiteData, areaName, groupByLevel, stat);
            resultList.add(result);
        }

        // 构建返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("date", date);
        response.put("query_level", groupByLevel);
        response.put("data", resultList);
        response.put("total_count", resultList.size());

        return response;
    }

    /**
     * 计算所有站点的数据
     */
    private List<Map<String, Object>> calculateAllSiteData(String city, String county, String township, String hamlet, String site, LocalDate date) {
        // 获取该区域下所有站点的考勤统计数据
        List<Map<String, Object>> siteStats = attendanceMapper.getAttendanceStatsByArea(
                city, county, township, hamlet, site, date, "site");

        // 查询所有加班数据
        List<Overtime> overtimeRecords = overtimeService.getOvertimesByAreaAndDateRange(
                city, county, township, hamlet, site, date, date);

        // 按站点分组加班数据
        Map<String, List<Overtime>> overtimeByArea = overtimeRecords.stream()
                .collect(Collectors.groupingBy(overtime -> overtime.getSite()));

        List<Map<String, Object>> allSiteData = new ArrayList<>();

        for (Map<String, Object> siteStat : siteStats) {
            Map<String, Object> siteData = new HashMap<>(siteStat);
            String siteName = (String) siteStat.get("area_name");

            // 计算该站点的加班时长
            List<Overtime> siteOvertimes = overtimeByArea.getOrDefault(siteName, new ArrayList<>());
            int totalOvertimeSeconds = siteOvertimes.stream()
                    .mapToInt(overtime -> overtime.getDuration() != null ? overtime.getDuration() : 0)
                    .sum();
            int totalOvertimeMinutes = totalOvertimeSeconds / 60;

            // 计算该站点的工作时间（单个站点，不需要合并）
            Integer totalWorkMinutes = calculateSingleSiteWorkMinutes(siteName, siteStat, date);

            // 计算该站点的在岗时间
            Integer onDutyMinutes = null;
            if (totalWorkMinutes != null && totalWorkMinutes > 0) {
                int totalLeavePostMinutes = calculateTotalLeavePostMinutes(siteName, "site",
                        (String) siteStat.get("city"), (String) siteStat.get("county"),
                        (String) siteStat.get("township"), (String) siteStat.get("hamlet"),
                        siteName, date);
                onDutyMinutes = Math.max(0, totalWorkMinutes - totalLeavePostMinutes);
            } else {
                onDutyMinutes = 0;
            }

            // 存储计算结果（用分钟，便于后续汇总）
            siteData.put("_total_work_minutes", totalWorkMinutes != null ? totalWorkMinutes : 0);
            siteData.put("_on_duty_minutes", onDutyMinutes != null ? onDutyMinutes : 0);
            siteData.put("_overtime_minutes", totalOvertimeMinutes);

            allSiteData.add(siteData);
        }

        return allSiteData;
    }

    /**
     * 计算单个站点的工作时间
     */
    private Integer calculateSingleSiteWorkMinutes(String siteName, Map<String, Object> siteStat, LocalDate date) {
        try {
            String city = (String) siteStat.get("city");
            String county = (String) siteStat.get("county");
            String township = (String) siteStat.get("township");
            String hamlet = (String) siteStat.get("hamlet");

            // 获取该站点的排班记录
            List<Map<String, Object>> scheduleList = usersMapper.getSchedules(
                    city, county, township, hamlet, siteName, date);

            if (scheduleList.isEmpty()) {
                return 0;
            }

            // 提取该站点的所有班次时间段
            List<TimeRange> timeRanges = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            LocalDate today = LocalDate.now();
            boolean isToday = date.equals(today);

            for (Map<String, Object> schedule : scheduleList) {
                Object startTimeObj = schedule.get("start_time");
                Object endTimeObj = schedule.get("end_time");

                if (startTimeObj != null && endTimeObj != null) {
                    String startTimeStr = startTimeObj.toString();
                    String endTimeStr = endTimeObj.toString();

                    // 处理时间格式
                    if (startTimeStr.length() == 5) {
                        startTimeStr += ":00";
                    }
                    if (endTimeStr.length() == 5) {
                        endTimeStr += ":00";
                    }

                    LocalDateTime startTime = LocalDateTime.parse(date + "T" + startTimeStr);
                    LocalDateTime endTime = LocalDateTime.parse(date + "T" + endTimeStr);

                    if (isToday) {
                        // 当天数据：只计算已完成或正在进行的班次
                        if (now.isBefore(startTime)) {
                            continue; // 班次还没开始，跳过
                        } else if (now.isAfter(startTime) && now.isBefore(endTime)) {
                            endTime = now; // 班次进行中，使用当前时间
                        }
                        // 班次已结束，使用原始结束时间
                    }

                    timeRanges.add(new TimeRange(startTime, endTime));
                }
            }

            // 合并重叠的时间段（单个站点可能有多个班次）
            List<TimeRange> mergedRanges = mergeTimeRanges(timeRanges);

            // 计算总时长
            int totalMinutes = 0;
            for (TimeRange range : mergedRanges) {
                totalMinutes += (int) ChronoUnit.MINUTES.between(range.start, range.end);
            }

            return totalMinutes;

        } catch (Exception e) {
            log.error("计算站点 {} 工作时间失败", siteName, e);
            return 0;
        }
    }

    /**
     * 按地域汇总站点数据
     */
    private Map<String, Object> aggregateSiteDataByArea(List<Map<String, Object>> allSiteData, String areaName, String groupByLevel, Map<String, Object> baseStat) {
        Map<String, Object> result = new HashMap<>();

        // 筛选属于该地域的站点数据
        List<Map<String, Object>> areaSiteData = allSiteData.stream()
                .filter(siteData -> {
                    switch (groupByLevel) {
                        case "county":
                            return areaName.equals(siteData.get("county"));
                        case "township":
                            return areaName.equals(siteData.get("township"));
                        case "hamlet":
                            return areaName.equals(siteData.get("hamlet"));
                        case "site":
                            return areaName.equals(siteData.get("area_name"));
                        default:
                            return areaName.equals(siteData.get("city"));
                    }
                })
                .collect(Collectors.toList());

        // 汇总工作时间数据
        int totalWorkMinutes = areaSiteData.stream()
                .mapToInt(siteData -> getIntegerValue(siteData, "_total_work_minutes"))
                .sum();

        int totalOnDutyMinutes = areaSiteData.stream()
                .mapToInt(siteData -> getIntegerValue(siteData, "_on_duty_minutes"))
                .sum();

        int totalOvertimeMinutes = areaSiteData.stream()
                .mapToInt(siteData -> getIntegerValue(siteData, "_overtime_minutes"))
                .sum();

        // 只保留需要的字段
        result.put("area_name", baseStat.get("area_name"));
        result.put("total_scheduled", baseStat.get("total_scheduled"));
        result.put("attended_count", baseStat.get("attended_count"));

        // 设置汇总结果（只返回小时字段）
        result.put("total_work_hours", String.format("%.1f", totalWorkMinutes / 60.0));
        result.put("on_duty_hours", String.format("%.1f", totalOnDutyMinutes / 60.0));
        result.put("overtime_hours", String.format("%.1f", totalOvertimeMinutes / 60.0));

        return result;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String prefix, String city, String county, String township, String hamlet, String site, LocalDate date, String groupByLevel) {
        StringBuilder keyBuilder = new StringBuilder(prefix);
        keyBuilder.append(":").append(date.toString());
        if (groupByLevel != null) {
            keyBuilder.append(":").append(groupByLevel);
        }

        if (city != null && !city.isEmpty()) {
            keyBuilder.append(":city:").append(city);
        }
        if (county != null && !county.isEmpty()) {
            keyBuilder.append(":county:").append(county);
        }
        if (township != null && !township.isEmpty()) {
            keyBuilder.append(":township:").append(township);
        }
        if (hamlet != null && !hamlet.isEmpty()) {
            keyBuilder.append(":hamlet:").append(hamlet);
        }
        if (site != null && !site.isEmpty()) {
            keyBuilder.append(":site:").append(site);
        }

        return keyBuilder.toString();
    }

    /**
     * 根据日期计算缓存过期时间
     */
    private CacheExpiration calculateCacheExpiration(LocalDate date) {
        LocalDate today = LocalDate.now();
        long daysAgo = ChronoUnit.DAYS.between(date, today);

        if (daysAgo <= 7) {
            return new CacheExpiration(1, TimeUnit.DAYS);      // 一周内：1天过期
        } else if (daysAgo <= 30) {
            return new CacheExpiration(7, TimeUnit.DAYS);      // 一月内：7天过期
        } else if (daysAgo <= 90) {
            return new CacheExpiration(30, TimeUnit.DAYS);     // 三月内：30天过期
        } else {
            return new CacheExpiration(90, TimeUnit.DAYS);     // 更久远：90天过期
        }
    }

    /**
     * 缓存过期时间配置类
     */
    private static class CacheExpiration {
        final long timeout;
        final TimeUnit unit;

        CacheExpiration(long timeout, TimeUnit unit) {
            this.timeout = timeout;
            this.unit = unit;
        }
    }

    /**
     * 构建月度缓存键
     */
    private String buildMonthlyCacheKey(String city, String county, String township, String hamlet, String site, int year, int month, String groupByLevel) {
        StringBuilder keyBuilder = new StringBuilder("attendance_monthly");
        keyBuilder.append(":").append(year).append("-").append(String.format("%02d", month));
        keyBuilder.append(":").append(groupByLevel);

        if (city != null && !city.isEmpty()) {
            keyBuilder.append(":city:").append(city);
        }
        if (county != null && !county.isEmpty()) {
            keyBuilder.append(":county:").append(county);
        }
        if (township != null && !township.isEmpty()) {
            keyBuilder.append(":township:").append(township);
        }
        if (hamlet != null && !hamlet.isEmpty()) {
            keyBuilder.append(":hamlet:").append(hamlet);
        }
        if (site != null && !site.isEmpty()) {
            keyBuilder.append(":site:").append(site);
        }

        return keyBuilder.toString();
    }

    /**
     * 从日度数据聚合月度数据 - 优化版本，使用批量查询
     */
    private Map<String, Object> aggregateMonthlyFromDailyOptimized(String city, String county, String township, String hamlet, String site, int year, int month, String groupByLevel) {
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        LocalDate today = LocalDate.now();

        // 如果结束日期超过今天，调整为今天
        if (endDate.isAfter(today)) {
            endDate = today;
        }

        // Map<String, Map<String, Object>> areaDataMap = new HashMap<>();

        // 直接使用逐日查询方式，确保数据准确性
        log.info("使用逐日查询方式计算月度数据，确保工作时间计算准确");
        return aggregateMonthlyFromDaily(city, county, township, hamlet, site, year, month, groupByLevel);
    }

    /**
     * 从日度数据聚合月度数据 - 多线程优化版本
     */
    private Map<String, Object> aggregateMonthlyFromDaily(String city, String county, String township, String hamlet, String site, int year, int month, String groupByLevel) {
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        LocalDate today = LocalDate.now();

        // 如果结束日期超过今天，调整为今天
        if (endDate.isAfter(today)) {
            endDate = today;
        }

        // 按地域汇总该月每天的数据
        Map<String, Map<String, Object>> areaDataMap = new HashMap<>();

        // 收集需要查询的日期
        List<LocalDate> datesToQuery = new ArrayList<>();
        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
            datesToQuery.add(currentDate);
        }

        log.info("开始多线程计算月度数据，共{}天", datesToQuery.size());

        // 使用多线程并行查询每日数据
        List<CompletableFuture<Map<String, Object>>> futures = datesToQuery.stream()
                .map(date -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 先尝试从缓存获取
                        String dailyCacheKey = buildCacheKey("attendance_result", city, county, township, hamlet, site, date, groupByLevel);
                        String cachedResult = redisUtils.get(dailyCacheKey);

                        if (cachedResult != null) {
                            try {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> cachedData = objectMapper.readValue(cachedResult, Map.class);
                                return cachedData;
                            } catch (Exception e) {
                                log.info("缓存数据解析失败，重新计算: {}", e.getMessage());
                            }
                        }

                        // 缓存未命中，计算数据
                        Map<String, Object> dailyData = calculateDailyAttendanceData(city, county, township, hamlet, site, date, groupByLevel);

                        // 存储到缓存
                        try {
                            String cacheValue = objectMapper.writeValueAsString(dailyData);
                            CacheExpiration expiration = calculateCacheExpiration(date);
                            redisUtils.setEx(dailyCacheKey, cacheValue, expiration.timeout, expiration.unit);
                        } catch (Exception e) {
                            log.info("存储缓存失败: {}", e.getMessage());
                        }

                        return dailyData;
                    } catch (Exception e) {
                        log.info("处理日期 {} 的数据时出错: {}", date, e.getMessage());
                        // 返回空数据结构
                        Map<String, Object> emptyData = new HashMap<>();
                        emptyData.put("data", new ArrayList<>());
                        return emptyData;
                    }
                }, attendanceTaskExecutor))
                .collect(Collectors.toList());

        // 等待所有任务完成并合并结果
        for (CompletableFuture<Map<String, Object>> future : futures) {
            try {
                Map<String, Object> dailyData = future.get();
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dailyList = (List<Map<String, Object>>) dailyData.get("data");

                if (dailyList != null) {
                    // 累加到月度数据中
                    for (Map<String, Object> dailyStat : dailyList) {
                        String areaName = (String) dailyStat.get("area_name");

                        Map<String, Object> monthlyData = areaDataMap.computeIfAbsent(areaName, k -> {
                            Map<String, Object> newData = new HashMap<>(dailyStat);
                            // 初始化累加字段（内部用分钟计算，最后转换为小时）
                            newData.put("_total_work_minutes", 0);
                            newData.put("_on_duty_minutes", 0);
                            newData.put("_overtime_minutes", 0);
                            newData.put("total_scheduled", 0);
                            newData.put("attended_count", 0);
                            return newData;
                        });

                        // 累加各项数据
                        addToMonthlyData(monthlyData, dailyStat);
                    }
                }
            } catch (Exception e) {
                log.info("合并日度数据时出错: {}", e.getMessage());
            }
        }

        log.info("多线程计算完成，共处理{}个地区的数据", areaDataMap.size());

        // 转换为结果列表并计算最终的比率
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> monthlyData : areaDataMap.values()) {
            calculateFinalRates(monthlyData);
            resultList.add(monthlyData);
        }

        // 构建返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("year", year);
        response.put("month", month);
        response.put("query_level", groupByLevel);
        response.put("data", resultList);
        response.put("total_count", resultList.size());

        return response;
    }



    /**
     * 累加日度数据到月度数据 - 原始版本
     */
    private void addToMonthlyData(Map<String, Object> monthlyData, Map<String, Object> dailyData) {
        // 从小时字段转换为分钟进行累加（内部计算用）
        Double dailyTotalWorkHours = getDoubleValue(dailyData, "total_work_hours");
        Double dailyOnDutyHours = getDoubleValue(dailyData, "on_duty_hours");
        Double dailyOvertimeHours = getDoubleValue(dailyData, "overtime_hours");
        Integer dailyScheduled = getIntegerValue(dailyData, "total_scheduled");
        Integer dailyAttended = getIntegerValue(dailyData, "attended_count");

        // 转换为分钟进行累加（使用内部字段）
        Integer dailyTotalWorkMinutes = (int) Math.round(dailyTotalWorkHours * 60);
        Integer dailyOnDutyMinutes = (int) Math.round(dailyOnDutyHours * 60);
        Integer dailyOvertimeMinutes = (int) Math.round(dailyOvertimeHours * 60);

        monthlyData.put("_total_work_minutes", getIntegerValue(monthlyData, "_total_work_minutes") + dailyTotalWorkMinutes);
        monthlyData.put("_on_duty_minutes", getIntegerValue(monthlyData, "_on_duty_minutes") + dailyOnDutyMinutes);
        monthlyData.put("_overtime_minutes", getIntegerValue(monthlyData, "_overtime_minutes") + dailyOvertimeMinutes);
        monthlyData.put("total_scheduled", getIntegerValue(monthlyData, "total_scheduled") + dailyScheduled);
        monthlyData.put("attended_count", getIntegerValue(monthlyData, "attended_count") + dailyAttended);
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    /**
     * 安全获取Double值
     */
    private Double getDoubleValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Double) {
            return (Double) value;
        }
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }

    /**
     * 处理最终数据格式
     */
    private void calculateFinalRates(Map<String, Object> monthlyData) {
        Integer totalWorkMinutes = getIntegerValue(monthlyData, "_total_work_minutes");
        Integer onDutyMinutes = getIntegerValue(monthlyData, "_on_duty_minutes");
        Integer overtimeMinutes = getIntegerValue(monthlyData, "_overtime_minutes");

        // 直接创建小时字段
        monthlyData.put("total_work_hours", String.format("%.1f", totalWorkMinutes / 60.0));
        monthlyData.put("on_duty_hours", String.format("%.1f", onDutyMinutes / 60.0));
        monthlyData.put("overtime_hours", String.format("%.1f", overtimeMinutes / 60.0));

        // 清理不需要的字段
        monthlyData.remove("_total_work_minutes");
        monthlyData.remove("_on_duty_minutes");
        monthlyData.remove("_overtime_minutes");

        // 清理地点字段，只保留area_name
        monthlyData.remove("city");
        monthlyData.remove("county");
        monthlyData.remove("township");
        monthlyData.remove("hamlet");
        monthlyData.remove("site");
    }

    /**
     * 构建年度缓存键
     */
    private String buildYearlyCacheKey(String city, String county, String township, String hamlet, String site, int year, String groupByLevel) {
        StringBuilder keyBuilder = new StringBuilder("attendance_yearly");
        keyBuilder.append(":").append(year);
        keyBuilder.append(":").append(groupByLevel);

        if (city != null && !city.isEmpty()) {
            keyBuilder.append(":city:").append(city);
        }
        if (county != null && !county.isEmpty()) {
            keyBuilder.append(":county:").append(county);
        }
        if (township != null && !township.isEmpty()) {
            keyBuilder.append(":township:").append(township);
        }
        if (hamlet != null && !hamlet.isEmpty()) {
            keyBuilder.append(":hamlet:").append(hamlet);
        }
        if (site != null && !site.isEmpty()) {
            keyBuilder.append(":site:").append(site);
        }

        return keyBuilder.toString();
    }

    /**
     * 从月度数据聚合年度数据 - 多线程优化版本
     */
    private Map<String, Object> aggregateYearlyFromMonthly(String city, String county, String township, String hamlet, String site, int year, String groupByLevel) {
        // 按地域汇总该年每月的数据
        Map<String, Map<String, Object>> areaDataMap = new HashMap<>();

        // 收集需要查询的月份
        List<Integer> monthsToQuery = new ArrayList<>();
        for (int month = 1; month <= 12; month++) {
            monthsToQuery.add(month);
        }

        log.info("开始多线程计算年度数据，共{}个月", monthsToQuery.size());

        // 使用多线程并行查询每月数据
        List<CompletableFuture<Map<String, Object>>> futures = monthsToQuery.stream()
                .map(month -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 先尝试从缓存获取月度数据
                        String monthlyCacheKey = buildMonthlyCacheKey(city, county, township, hamlet, site, year, month, groupByLevel);
                        String cachedResult = redisUtils.get(monthlyCacheKey);

                        if (cachedResult != null) {
                            try {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> cachedData = objectMapper.readValue(cachedResult, Map.class);
                                return cachedData;
                            } catch (Exception e) {
                                log.info("月度缓存数据解析失败，重新计算: {}", e.getMessage());
                            }
                        }

                        // 缓存未命中，计算月度数据
                        Map<String, Object> monthlyData = aggregateMonthlyFromDaily(city, county, township, hamlet, site, year, month, groupByLevel);

                        // 存储到缓存（如果不是当前月份）
                        LocalDate today = LocalDate.now();
                        boolean isCurrentMonth = (year == today.getYear() && month == today.getMonthValue());
                        if (!isCurrentMonth) {
                            try {
                                String cacheValue = objectMapper.writeValueAsString(monthlyData);
                                redisUtils.setEx(monthlyCacheKey, cacheValue, 30, TimeUnit.DAYS);
                            } catch (Exception e) {
                                log.info("存储月度缓存失败: {}", e.getMessage());
                            }
                        }

                        return monthlyData;
                    } catch (Exception e) {
                        log.info("处理年份 {} 月份 {} 的数据时出错: {}", year, month, e.getMessage());
                        // 返回空数据结构
                        Map<String, Object> emptyData = new HashMap<>();
                        emptyData.put("data", new ArrayList<>());
                        return emptyData;
                    }
                }, attendanceTaskExecutor))
                .collect(Collectors.toList());

        // 等待所有任务完成并合并结果
        for (CompletableFuture<Map<String, Object>> future : futures) {
            try {
                Map<String, Object> monthlyData = future.get();
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> monthlyList = (List<Map<String, Object>>) monthlyData.get("data");

                if (monthlyList != null) {
                    // 累加到年度数据中
                    for (Map<String, Object> monthlyStat : monthlyList) {
                        String areaName = (String) monthlyStat.get("area_name");

                        Map<String, Object> yearlyData = areaDataMap.computeIfAbsent(areaName, k -> {
                            Map<String, Object> newData = new HashMap<>(monthlyStat);
                            // 初始化累加字段（内部用分钟计算，最后转换为小时）
                            newData.put("_total_work_minutes", 0);
                            newData.put("_on_duty_minutes", 0);
                            newData.put("_overtime_minutes", 0);
                            newData.put("total_scheduled", 0);
                            newData.put("attended_count", 0);
                            return newData;
                        });

                        // 累加各项数据
                        addToMonthlyData(yearlyData, monthlyStat);
                    }
                }
            } catch (Exception e) {
                log.info("合并月度数据时出错: {}", e.getMessage());
            }
        }

        log.info("多线程年度计算完成，共处理{}个地区的数据", areaDataMap.size());

        // 转换为结果列表并计算最终的比率
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> yearlyData : areaDataMap.values()) {
            calculateFinalRates(yearlyData);
            resultList.add(yearlyData);
        }

        // 构建返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("year", year);
        response.put("query_level", groupByLevel);
        response.put("data", resultList);
        response.put("total_count", resultList.size());

        return response;
    }

    /**
     * 合并重叠的时间段
     */
    private List<TimeRange> mergeTimeRanges(List<TimeRange> timeRanges) {
        if (timeRanges.isEmpty()) {
            return new ArrayList<>();
        }

        // 按开始时间排序
        timeRanges.sort(Comparator.comparing(range -> range.start));

        List<TimeRange> merged = new ArrayList<>();
        TimeRange current = timeRanges.get(0);

        for (int i = 1; i < timeRanges.size(); i++) {
            TimeRange next = timeRanges.get(i);

            if (current.end.isAfter(next.start) || current.end.equals(next.start)) {
                // 有重叠或相邻，合并
                current = new TimeRange(current.start,
                    current.end.isAfter(next.end) ? current.end : next.end);
            } else {
                // 没有重叠，添加当前时间段，开始新的时间段
                merged.add(current);
                current = next;
            }
        }
        merged.add(current);

        return merged;
    }

    /**
     * 时间段类
     */
    private static class TimeRange {
        final LocalDateTime start;
        final LocalDateTime end;

        TimeRange(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
    }

    /**
     * 计算总脱岗时间（去重后的所有人都脱岗的时间段）
     */
    private int calculateTotalLeavePostMinutes(String areaName, String groupByLevel, String city, String county,
                                             String township, String hamlet, String site, LocalDate date) {
        try {
            // 根据地域层级和地域名称构建查询条件
            String targetCity = null, targetCounty = null, targetTownship = null, targetHamlet = null, targetSite = null;

            switch (groupByLevel) {
                case "county":
                    targetCity = city;
                    targetCounty = areaName;
                    break;
                case "township":
                    targetCity = city;
                    targetCounty = county;
                    targetTownship = areaName;
                    break;
                case "hamlet":
                    targetCity = city;
                    targetCounty = county;
                    targetTownship = township;
                    targetHamlet = areaName;
                    break;
                case "site":
                    targetCity = city;
                    targetCounty = county;
                    targetTownship = township;
                    targetHamlet = hamlet;
                    targetSite = areaName;
                    break;
                default:
                    targetCity = areaName;
                    break;
            }

            // 获取该地域所有员工的脱岗记录
            List<LeavePost> leavePostRecords = leavePostMapper.getLeavePostsByDate(
                    targetCity, targetCounty, targetTownship, targetHamlet, targetSite, date);

            if (leavePostRecords.isEmpty()) {
                return 0;
            }

            // 获取该地域当天有排班的员工ID
            List<Map<String, Object>> scheduleList = usersMapper.getSchedules(
                    targetCity, targetCounty, targetTownship, targetHamlet, targetSite, date);

            Set<Integer> allUserIds = scheduleList.stream()
                    .map(record -> (Integer) record.get("user_id"))
                    .collect(Collectors.toSet());

            if (allUserIds.isEmpty()) {
                return 0;
            }

            // 按时间段分析，找出所有人都脱岗的时间段
            return calculateAllOffDutyPeriods(leavePostRecords, allUserIds, date);

        } catch (Exception e) {
            log.error("计算总脱岗时间失败", e);
            return 0;
        }
    }

    /**
     * 计算所有人都脱岗的时间段总时长
     */
    private int calculateAllOffDutyPeriods(List<LeavePost> leavePostRecords, Set<Integer> allUserIds, LocalDate date) {
        // 创建时间事件列表（脱岗开始和结束事件）
        List<TimeEvent> events = new ArrayList<>();

        for (LeavePost leavePost : leavePostRecords) {
            if (leavePost.getStartTime() != null) {
                // 将Date转换为LocalDateTime
                LocalDateTime startTime = leavePost.getStartTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                events.add(new TimeEvent(startTime, leavePost.getUserId(), true)); // 脱岗开始

                LocalDateTime endTime;
                if (Boolean.TRUE.equals(leavePost.getStatus()) && leavePost.getEndTime() != null) {
                    // 已结束的脱岗记录（status为true表示已结束）
                    endTime = leavePost.getEndTime().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalDateTime();
                } else {
                    // 未结束的脱岗记录，使用当前时间或当天结束时间
                    LocalDateTime now = LocalDateTime.now();
                    LocalDateTime dayEnd = date.atTime(23, 59, 59);
                    endTime = now.isBefore(dayEnd) ? now : dayEnd;
                }
                events.add(new TimeEvent(endTime, leavePost.getUserId(), false)); // 脱岗结束
            }
        }

        // 按时间排序
        events.sort(Comparator.comparing(TimeEvent::getTime));

        // 扫描时间线，找出所有人都脱岗的时间段
        Set<Integer> currentOffDutyUsers = new HashSet<>();
        int totalOffDutyMinutes = 0;
        LocalDateTime allOffDutyStart = null;

        for (TimeEvent event : events) {
            // 检查当前是否所有人都脱岗
            boolean wasAllOffDuty = currentOffDutyUsers.size() == allUserIds.size();

            // 更新脱岗用户集合
            if (event.isStart()) {
                currentOffDutyUsers.add(event.getUserId());
            } else {
                currentOffDutyUsers.remove(event.getUserId());
            }

            // 检查更新后是否所有人都脱岗
            boolean isAllOffDuty = currentOffDutyUsers.size() == allUserIds.size();

            if (!wasAllOffDuty && isAllOffDuty) {
                // 开始所有人都脱岗的时间段
                allOffDutyStart = event.getTime();
            } else if (wasAllOffDuty && !isAllOffDuty && allOffDutyStart != null) {
                // 结束所有人都脱岗的时间段
                long minutes = ChronoUnit.MINUTES.between(allOffDutyStart, event.getTime());
                totalOffDutyMinutes += (int) minutes;
                allOffDutyStart = null;
            }
        }

        return totalOffDutyMinutes;
    }

    /**
     * 时间事件类，用于表示脱岗开始或结束事件
     */
    private static class TimeEvent {
        private final LocalDateTime time;
        private final Integer userId;
        private final boolean start; // true表示脱岗开始，false表示脱岗结束

        public TimeEvent(LocalDateTime time, Integer userId, boolean start) {
            this.time = time;
            this.userId = userId;
            this.start = start;
        }

        public LocalDateTime getTime() { return time; }
        public Integer getUserId() { return userId; }
        public boolean isStart() { return start; }
    }

    @Override
    public SaResult getAttendanceAndWorkTimeByMonth(String city, String county, String township, String hamlet, String site, Date date) {
        try {
            // 将Date转换为LocalDate，获取年月
            LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            int year = localDate.getYear();
            int month = localDate.getMonthValue();

            // 检查是否为当月
            LocalDate today = LocalDate.now();
            boolean isCurrentMonth = (year == today.getYear() && month == today.getMonthValue());

            String groupByLevel = getTheNextLevel(city, county, township, hamlet, site);
            String monthlyCacheKey = buildMonthlyCacheKey(city, county, township, hamlet, site, year, month, groupByLevel);

            if (!isCurrentMonth) {
                // 历史月份：尝试获取月度缓存
                String cachedMonthlyResult = redisUtils.get(monthlyCacheKey);

                if (cachedMonthlyResult != null) {
                    try {
                        Map<String, Object> response = objectMapper.readValue(cachedMonthlyResult, Map.class);
                        log.info("月度缓存命中: {}", monthlyCacheKey);
                        return SaResult.data(response);
                    } catch (Exception e) {
                        log.info("月度缓存解析失败: {}", e.getMessage());
                        redisUtils.delete(monthlyCacheKey);
                    }
                }
            }

            // 利用日度数据进行月度汇总 - 使用优化版本
            Map<String, Object> monthlyResult = aggregateMonthlyFromDailyOptimized(city, county, township, hamlet, site, year, month, groupByLevel);

            if (!isCurrentMonth) {
                // 存储月度缓存
                try {
                    String cacheValue = objectMapper.writeValueAsString(monthlyResult);
                    redisUtils.setEx(monthlyCacheKey, cacheValue, 30, TimeUnit.DAYS); // 30天过期
                    log.info("月度缓存存储: {}", monthlyCacheKey);
                } catch (Exception e) {
                    log.info("存储月度缓存失败: {}", e.getMessage());
                }
            }

            return SaResult.data(monthlyResult);

        } catch (Exception e) {
            log.error("查询月度考勤统计失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public SaResult getAttendanceAndWorkTimeByYears(String city, String county, String township, String hamlet, String site, Date date) {
        try {
            // 将Date转换为LocalDate，获取年份
            LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            int year = localDate.getYear();

            // 检查是否为当年
            LocalDate today = LocalDate.now();
            boolean isCurrentYear = (year == today.getYear());

            String groupByLevel = getTheNextLevel(city, county, township, hamlet, site);
            String yearlyCacheKey = buildYearlyCacheKey(city, county, township, hamlet, site, year, groupByLevel);
            if (!isCurrentYear) {
                // 历史年份：尝试获取年度缓存
                String cachedYearlyResult = redisUtils.get(yearlyCacheKey);

                if (cachedYearlyResult != null) {
                    try {
                        Map<String, Object> response = objectMapper.readValue(cachedYearlyResult, Map.class);
                        log.info("年度缓存命中: {}", yearlyCacheKey);
                        return SaResult.data(response);
                    } catch (Exception e) {
                        log.info("年度缓存解析失败: {}", e.getMessage());
                        redisUtils.delete(yearlyCacheKey);
                    }
                }
            }

            // 利用月度数据进行年度汇总
            Map<String, Object> yearlyResult = aggregateYearlyFromMonthly(city, county, township, hamlet, site, year, groupByLevel);

            if (!isCurrentYear) {
                // 存储年度缓存
                try {
                    String cacheValue = objectMapper.writeValueAsString(yearlyResult);
                    redisUtils.setEx(yearlyCacheKey, cacheValue, 90, TimeUnit.DAYS); // 90天过期
                    log.info("年度缓存存储: {}", yearlyCacheKey);
                } catch (Exception e) {
                    log.info("存储年度缓存失败: {}", e.getMessage());
                }
            }

            return SaResult.data(yearlyResult);

        } catch (Exception e) {
            log.error("查询年度考勤统计失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 判断设备是否在线
     * @param devices 设备列表
     * @return 设备是否在线
     */
    private boolean isDeviceOnline(List<Device> devices) {
        if (devices == null || devices.isEmpty()) {
            return false; // 如果没有设备，认为离线
        }

        // 过滤出电脑类型的设备
//        List<Device> computerDevices = devices.stream()
//            .filter(device -> "电脑".equals(device.getDeviceType()))
//            .collect(Collectors.toList());
//        if (computerDevices.isEmpty()) {
//            return false; // 如果没有电脑类型设备，认为离线
//        }

        // 逐个查询每台设备的状态（确保按点位区分）
        for (Device device : devices) {
            // 查询设备最新的未结束状态记录（不限制state值）
            DeviceStatusHistory latestStatus = deviceStatusHistoryMapper.selectOne(
                new QueryWrapper<DeviceStatusHistory>()
                    .eq("equipment_number", device.getEquipmentNumber())
                    .eq("ip", device.getIp())
                    .isNull("end_time") // 查询未结束的记录
                    // 移除 .eq("state", 1) 限制条件，查询最新的未结束记录
                    .orderByDesc("start_time")
                    .last("LIMIT 1")
            );

            // 如果有未结束的状态记录，直接判断其状态
            if (latestStatus != null) {
                if (latestStatus.getState() == 1) {
                    return false; // 最新状态是离线，整个点位就是故障
                }
            } else {
                // 如果没有状态历史记录，回退到device表的状态判断
                if (device.getState() == 1) {
                    return false; // device表显示离线，整个点位就是故障
                }
            }
        }

        return true; // 所有设备都在线
    }

    /**
     * 判断人员状态
     *
     * @param userId          用户ID
     * @param schedules       用户排班列表
     * @param isDeviceOnline  设备是否在线
     * @param locationInfo    地点信息
     * @param timeSlot        时间段 (am/pm)
     * @param abnormalRecords 异常记录列表，用于添加异常记录
     * @param deptName
     * @return 人员状态及是否为异常状态
     */
    private Map<String, Object> determineStaffStatus(
            Integer userId,
            List<Schedule> schedules,
            boolean isDeviceOnline,
            Map<String, String> locationInfo,
            String timeSlot,
            List<Map<String, Object>> abnormalRecords,
            String deptName) {
        // 构建点位唯一标识，使用下划线作为分隔符
        String locationKey = String.format("%s_%s_%s_%s_%s",
                locationInfo.get("city"),
                locationInfo.get("county"),
                locationInfo.get("township"),
                locationInfo.get("hamlet"),
                locationInfo.get("site"));
        // 保存点位状态到Redis，区分上午和下午
        String redisKey = "site_status:" + locationKey + ":" + timeSlot;
        String redisStatus = redisUtils.get(redisKey);
        Map<String, Object> status = new HashMap<>();
        
        // 1. 检查是否在排班时间内
        boolean isInScheduleTime = false;  //是否在排班时间
        boolean hasLeaveRecord = false;     //是否请假
        for (Schedule schedule : schedules) {
            if (schedule.getUserId().equals(userId)) {
                // 从shift表中获取班次信息
                Shift shift = shiftMapper.selectById(schedule.getShiftId());
                if (shift != null) {
                    // 将LocalTime转换为String
                    String shiftStartTime = shift.getStartTime().toString();
                    String shiftEndTime = shift.getEndTime().toString();
                    if (isTimeInRange(timeSlot, shiftStartTime, shiftEndTime)) {
                        isInScheduleTime = true;
                        LocalDateTime scheduleDate = schedule.getScheduleDate();
                        // 提取日期部分
                        LocalDate datePart = scheduleDate.toLocalDate();
                        // 解析时间部分
                        LocalTime timeStart = LocalTime.parse(shiftStartTime);
                        LocalTime timeEnd = LocalTime.parse(shiftEndTime);
                        // 合并成完整的日期时间
                        LocalDateTime startDateTime = LocalDateTime.of(datePart, timeStart);
                        LocalDateTime endDateTime = LocalDateTime.of(datePart, timeEnd);
                        //检查用户是否有请假记录
                        QueryWrapper<Leave> leaveQueryWrapper = new QueryWrapper<>();
                        leaveQueryWrapper.eq("user_id", userId);
                        leaveQueryWrapper.eq("status", LeaveStatusEnum.APPROVED.getCode());
                        leaveQueryWrapper.and(wrapper -> wrapper
                                .or(w -> w.ge("start_time", startDateTime).le("start_time", endDateTime)) // 情况1
                                .or(w -> w.ge("end_time", startDateTime).le("end_time", endDateTime))     // 情况2
                                .or(w -> w.le("start_time", startDateTime).ge("end_time", endDateTime))   // 情况3
                        );
                        hasLeaveRecord=leaveMapper.selectCount(leaveQueryWrapper) > 0;
                        break;
                    }
                }
            }
        }
        // 2. 检查是否有异常记录
        boolean hasAbnormalRecord = false;      //是否脱岗
        QueryWrapper<LeavePost> leavePostQueryWrapper=new QueryWrapper<>();
        leavePostQueryWrapper.eq("user_id", userId);
        leavePostQueryWrapper.eq("status", false);
        if(!hasLeaveRecord){
            hasAbnormalRecord=leavePostMapper.selectCount(leavePostQueryWrapper) > 0;
        }
        // 3. 判断状态
        if (!isDeviceOnline) {
            status.put("status", "故障");
        } else if ("在岗".equals(redisStatus)&&"专职".equals(deptName)) {
            status.put("status", "在岗");
        } else if (!isInScheduleTime) {
            status.put("status", "休息");
        } else if (hasLeaveRecord) {
            status.put("status", "请假");
        } else if (hasAbnormalRecord) {
            status.put("status", "脱岗");
        } else {
            status.put("status", "在岗");
        }
        
        // 添加额外的状态信息
        status.put("isAbnormal", hasAbnormalRecord);
        status.put("isInScheduleTime", isInScheduleTime);
        status.put("hasLeaveRecord", hasLeaveRecord);
        return status;
    }

    /**
     * 判断点位状态
     *
     * @param isDeviceOnline   设备是否在线
     * @param hasAbnormalStaff 是否有异常人员
     * @param isInScheduleTime 是否在排班时间内
     * @param hasLeaveRecord
     * @param redisKey         点位redis判断key
     * @return 点位状态
     */
    private String determineSiteStatus(boolean isDeviceOnline, boolean hasAbnormalStaff, boolean isInScheduleTime, boolean hasLeaveRecord, String redisKey) {
        // 1. 设备离线时，点位状态为故障
        if (!isDeviceOnline) {
            return "故障";
        }
        // 2.2 从Redis中获取状态
        String status = redisUtils.get(redisKey);
        if ("在岗".equals(status)) {
            return "在岗";
        }
        // 2. 在排班时间内
        if (isInScheduleTime) {
            // 2.1 如果所有人都是异常人员，点位状态为脱岗
            if (hasAbnormalStaff) {
                return "脱岗";
            }
            if (hasLeaveRecord) {
                return "请假";
            }
            // 2.3 如果Redis中没有状态，默认为在岗（因为至少有一人在岗）
            return "在岗";
        }
        
        // 3. 非排班时间
        return "休息";
    }

    /**
     * 拼接完整的地点信息
     *
     * @param city     城市
     * @param county   区县
     * @param township 乡镇
     * @param hamlet   村
     * @param site     详细地址
     * @return 完整的地点信息字符串
     */
    private String concatenateLocation(String city, String county, String township, String hamlet, String site) {
        StringBuilder locationBuilder = new StringBuilder();
        boolean first = true;

        //if (StringUtils.isNotBlank(city)) {
        //    locationBuilder.append(city);
        //    first = false;
        //}
        if (StringUtils.isNotBlank(county)) {
            if (!first) {
                locationBuilder.append("-");
            }
            locationBuilder.append(county);
            first = false;
        }
        if (StringUtils.isNotBlank(township)) {
            if (!first) {
                locationBuilder.append("-");
            }
            locationBuilder.append(township);
            first = false;
        }
        if (StringUtils.isNotBlank(hamlet)) {
            if (!first) {
                locationBuilder.append("-");
            }
            locationBuilder.append(hamlet);
            first = false;
        }
        if (StringUtils.isNotBlank(site)) {
            if (!first) {
                locationBuilder.append("-");
            }
            locationBuilder.append(site);
            first = false;
        }
        return locationBuilder.toString().trim();
    }


    @Override
    public SaResult selectIllegalRecords(SelectIllegalRecords selectIllegalRecords) {
        IPage<IllegalRecords> page = new Page<>();
        page.setCurrent(selectIllegalRecords.getCurPage());
        page.setSize(selectIllegalRecords.getPageSize());
        QueryWrapper<IllegalRecords> queryWrapper = getIllegalRecordsQueryWrapper(selectIllegalRecords);
        queryWrapper.orderByDesc("create_time");
        IPage<IllegalRecords> illegalRecordsIPage = illegalRecordsMapper.selectPage(page, queryWrapper);
        List<IllegalRecords> records = illegalRecordsIPage.getRecords();
        for (IllegalRecords record : records) {
            String pictureUrl = record.getPictureUrl();
            if (pictureUrl != null && !pictureUrl.isEmpty()) {
                String[] pictureUrls = pictureUrl.split(",");
                ArrayList<String> urlList = new ArrayList<>();
                for (String url : pictureUrls) {
                    String expireUrl = minioUtil.getExpireFileUrl(url, 1, HOURS);
                    urlList.add(expireUrl);
                }
                // 将 ArrayList<String> 转换为 JSON 字符串
                String jsonUrlList = JSON.toJSONString(urlList);
                record.setPictureUrl(jsonUrlList);
            } else {
                // 处理 pictureUrl 为 null 或空字符串的情况
                record.setPictureUrl("[]");
            }
        }
        return SaResult.data(illegalRecordsIPage);
    }

    @Override
    @Transactional
    public SaResult assignACounselor(AssignACounselor assignACounselor) {
        List<Integer> userId = assignACounselor.getUserId();
        String uuid = assignACounselor.getUuid();
        QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("illegal_records_uuid", uuid);
        accuratePersuasionMapper.delete(queryWrapper);
        for (Integer integer : userId) {
            String userName = usersMapper.selectById(integer).getName();
            AccuratePersuasion accuratePersuasion = new AccuratePersuasion();
            accuratePersuasion.setIllegalName(assignACounselor.getIllegalName());
            accuratePersuasion.setUuid(IdUtil.simpleUUID());
            accuratePersuasion.setUserId(integer);
            accuratePersuasion.setUserName(userName);
            accuratePersuasion.setIllegalRecordsUuid(uuid);
            accuratePersuasion.setDisposalStatus(0);
            Date date = new Date();
            Date newDate = DateUtil.offset(date, DateField.DAY_OF_MONTH, 5);
            accuratePersuasion.setDeadlineTime(newDate);    //截止处理时间
            accuratePersuasionMapper.insert(accuratePersuasion);
        }
        IllegalRecords illegalRecords = new IllegalRecords();
        //illegalRecords.setHandMovement(ASSIGNED);
        illegalRecords.setDisposalStatus(SENT_DOWN);
        QueryWrapper<IllegalRecords> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("UUID", uuid);
        illegalRecordsMapper.update(illegalRecords, queryWrapper1);
        return SaResult.ok("指派成功");
    }

    @Override
    public SaResult selectProselytizer(IocationDTO ionationDTO) {
        List<Users> users = usersMapper.selectProselytizer(ionationDTO.getCity(), ionationDTO.getCounty(),
                ionationDTO.getTownship(), ionationDTO.getHamlet(), ionationDTO.getSite());
        if (users.isEmpty()) {
            return SaResult.empty();
        } else {
            return SaResult.data(users);
        }
    }

    @Override
    public SaResult selectProselytizerList(IocationDTO ionationDTO) {
        IPage<Users> page = new Page<>();
        page.setCurrent(ionationDTO.getCurPage());
        page.setSize(ionationDTO.getPageSize());
        IPage<Map<String, Object>> users = usersMapper.selectProselytizerListMap(page, ionationDTO.getCity(), ionationDTO.getCounty(),
                ionationDTO.getTownship(), ionationDTO.getHamlet(), ionationDTO.getSite());
        return SaResult.data(users);
    }

    @Override
    public SaResult selectSentDown(String uuid) {
        QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("illegal_records_uuid", uuid);
        List<AccuratePersuasion> accuratePersuasions = accuratePersuasionMapper.selectList(queryWrapper);
        PersuasionInDetail persuasionInDetail = new PersuasionInDetail();
        if (!accuratePersuasions.isEmpty()) {
            persuasionInDetail.setUserNames(accuratePersuasions.stream().map(AccuratePersuasion::getUserName).collect(Collectors.toList()));
            persuasionInDetail.setName(accuratePersuasions.get(0).getUserName());
            persuasionInDetail.setDisposalMethod(accuratePersuasions.get(0).getDisposalMethod());
            persuasionInDetail.setDisposalStatus(accuratePersuasions.get(0).getDisposalStatus());
            persuasionInDetail.setImgl(accuratePersuasions.get(0).getImgl());
            persuasionInDetail.setRemarks(accuratePersuasions.get(0).getRemarks());
            persuasionInDetail.setProcessingTime(accuratePersuasions.get(0).getProcessingTime());
            persuasionInDetail.setCreateTime(accuratePersuasions.get(0).getCreateTime());
        } else {
            return SaResult.empty();
        }
        return SaResult.data(accuratePersuasions);
    }

    @Override
    public SaResult selectDetails(String uuid) {
        QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("UUID", uuid);
        IllegalRecords illegalRecords = illegalRecordsMapper.selectOne(queryWrapper);
        if (illegalRecords != null) {
            String pictureUrl = illegalRecords.getPictureUrl();
            if (pictureUrl != null && !pictureUrl.isEmpty()) {
                String[] pictureUrls = pictureUrl.split(",");
                ArrayList<String> urlList = new ArrayList<>();
                for (String url : pictureUrls) {
                    String expireUrl = minioUtil.getExpireFileUrl(url, 1, HOURS);
                    urlList.add(expireUrl);
                }
                // 将 ArrayList<String> 转换为 JSON 字符串
                String jsonUrlList = JSON.toJSONString(urlList);
                illegalRecords.setPictureUrl(jsonUrlList);
            }
            //todo 后期删除的
//            String actualPictureUrl = illegalRecords.getActualPictureUrl();
//            if (actualPictureUrl != null && !actualPictureUrl.isEmpty()) {
//                String[] actualPictureUrls = actualPictureUrl.split(",");
//                ArrayList<String> urlList = new ArrayList<>();
//                for (String url : actualPictureUrls) {
//                    String expireUrl = minioUtil.getExpireFileUrl(url, 1, HOURS);
//                    urlList.add(expireUrl);
//                }
//                // 将 ArrayList<String> 转换为 JSON 字符串
//                String jsonUrlList = JSON.toJSONString(urlList);
//                illegalRecords.setActualPictureUrl(jsonUrlList);
//            }



            if (StringUtils.isNotBlank(illegalRecords.getVideoUrl())) {
                String expireFileUrl = minioUtil.getExpireFileUrl(illegalRecords.getVideoUrl(), 1, HOURS);
                illegalRecords.setVideoUrl(expireFileUrl);
            }
        }
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("illegalRecords", illegalRecords);
        QueryWrapper<AccuratePersuasion> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("illegal_records_uuid", uuid);
        List<AccuratePersuasion> accuratePersuasions = accuratePersuasionMapper.selectList(queryWrapper1);
        StringBuilder handlerNames = new StringBuilder();
        if (!accuratePersuasions.isEmpty()) {
            for (AccuratePersuasion accuratePersuasion : accuratePersuasions) {
                if (!handlerNames.isEmpty()) {
                    handlerNames.append(", ");
                }
                handlerNames.append(accuratePersuasion.getUserName());
            }
            AccuratePersuasion accuratePersuasion = accuratePersuasions.get(0);
            String imagePath = accuratePersuasions.get(0).getImgl();
            if (imagePath != null && !imagePath.isEmpty()) {
                String[] pictureUrls = imagePath.split(",");
                ArrayList<String> imgLits = new ArrayList<>();
                for (String url : pictureUrls) {
                    String expireUrl = minioUtil.getExpireFileUrl(url, 1, HOURS);
                    imgLits.add(expireUrl);
                }
                // 将 ArrayList<String> 转换为 JSON 字符串
                String jsonUrlList = JSON.toJSONString(imgLits);
                accuratePersuasion.setImgl(jsonUrlList);
            }
            accuratePersuasion.setUserName(handlerNames.toString());
            objectObjectHashMap.put("accuratePersuasion", accuratePersuasion);
        }
        return SaResult.data(objectObjectHashMap);
    }

    @Override
    public SaResult todayStatistics() {
        // 获取当前日期
        Date today = new Date();
        // 获取一周前的日期
        Date oneWeekAgo = DateUtil.offsetDay(today, -7);
        // 查询今天的违法记录数
        QueryWrapper<IllegalRecords> todayQueryWrapper = new QueryWrapper<>();
        todayQueryWrapper.ne("illegal_type", 0);
        todayQueryWrapper.eq("date_format(create_time,'%Y-%m-%d')", DateUtil.format(today, "yyyy-MM-dd"));
        Long todayCount = illegalRecordsMapper.selectCount(todayQueryWrapper);
        // 查询一周前的违法记录数
        QueryWrapper<IllegalRecords> oneWeekAgoQueryWrapper = new QueryWrapper<>();
        oneWeekAgoQueryWrapper.ne("illegal_type", 0);
        oneWeekAgoQueryWrapper.eq("date_format(create_time,'%Y-%m-%d')", DateUtil.format(oneWeekAgo, "yyyy-MM-dd"));
        Long oneWeekAgoCount = illegalRecordsMapper.selectCount(oneWeekAgoQueryWrapper);
        // 计算违法率提升百分比
        double increaseRate = 0.0;
        if (oneWeekAgoCount != 0) {
            increaseRate = ((double) (todayCount - oneWeekAgoCount) / oneWeekAgoCount) * 100;
            increaseRate = Math.round(increaseRate * 100.0) / 100.0; // 保留两位小数
        }
        // 封装结果
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("todayCount", todayCount);
        result.put("oneWeekAgoCount", oneWeekAgoCount);
        result.put("increaseRate", increaseRate);
        return SaResult.data(result);
    }

    @Override
    public SaResult getViolationTrendsByWeek(Date startTime, Date endTime, String city, String county, String township, String hamlet, String site) {
        // 直接使用传入的时间范围，不需要重新计算
        List<ViolationTrend> trends = illegalRecordsMapper.selectViolationTrendsByWeek(startTime, endTime, city, county, township, hamlet, site);
        return SaResult.data(formatForECharts(trends, 7, "week", startTime, endTime));
    }

    @Override
    public SaResult getViolationTrendsByMonth(int year, int month, String city, String county, String township, String hamlet, String site) {
        List<ViolationTrend> trends = illegalRecordsMapper.selectViolationTrendsByMonth(year, month, city, county, township, hamlet, site);
        YearMonth yearMonth = YearMonth.of(year, month);
        int daysInMonth = yearMonth.lengthOfMonth();
        return SaResult.data(formatForECharts(trends, daysInMonth, "day", null, null)); // 31天
    }

    @Override
    public SaResult getViolationTrendsByYear(int year, String city, String county, String township, String hamlet, String site) {
        List<ViolationTrend> trends = illegalRecordsMapper.selectViolationTrendsByYear(year, city, county, township, hamlet, site);
        return SaResult.data(formatForECharts(trends, 12, "month", null, null)); // 12个月
    }

    @Override
    public SaResult getViolationTrendsByDay(LocalDate date, String city, String county, String township, String hamlet, String site) {
        List<ViolationTrend> trends = illegalRecordsMapper.selectViolationTrendsByDay(date, city, county, township, hamlet, site);
        return SaResult.data(formatForECharts(trends, 24, "hour", null, null)); // 24小时
    }

    @Override
    public SaResult getViolationCountsByDay(LocalDate date, String city, String county, String township, String hamlet, String site) {
        List<ViolationCount> counts = illegalRecordsMapper.selectViolationCountsByDay(date, city, county, township, hamlet, site);
        ArrayList<Object> list = new ArrayList<>();
        int i = 0;
        for (ViolationCount entry : counts) {
            String type;
            int period;
            int count;
            if (entry instanceof ViolationCount) {
                ViolationCount countEntry = (ViolationCount) entry;
                type = countEntry.getIllegalType().getDesc();
                period = countEntry.getIllegalType().getCode();
                count = countEntry.getCount();
            } else {
                continue;
            }
            QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("illegal_type", period);
            queryWrapper.eq("DATE(create_time)", date);
            queryWrapper.in("disposal_status", 2, 3);
            Long sentDown = illegalRecordsMapper.selectCount(queryWrapper);//已下派
            QueryWrapper<IllegalRecords> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("illegal_type", period);
            queryWrapper1.eq("DATE(create_time)", date);
            queryWrapper1.eq("disposal_status", 2);
            Long processed = illegalRecordsMapper.selectCount(queryWrapper1);//已处理
            double handleRate = 0.0;
            if (sentDown != 0) {
                handleRate = (double) processed / sentDown * 100;
            }
            i = i + 1;
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("id", i);
            objectObjectHashMap.put("name", type);      //类型
            objectObjectHashMap.put("data", count);     //违法数量
            objectObjectHashMap.put("send", sentDown);  //已下派
            objectObjectHashMap.put("handled", processed);//已处理
            objectObjectHashMap.put("handleRate", handleRate);  //处理率
            list.add(objectObjectHashMap);
        }
        return SaResult.data(list); // 使用合并后的方法
    }

    @Override
    public SaResult getViolationCountsByTimeRange(Date startTime, Date endTime, String city, String county, String township, String hamlet, String site) {
        List<ViolationCount> counts = illegalRecordsMapper.selectViolationCountsByTimeRange(startTime, endTime, city, county, township, hamlet, site);
//        Map<String, Object> stringObjectMap = formatForECharts(counts, 1, "count", null, null);// 使用合并后的方法
        ArrayList<Object> list = new ArrayList<>();
        int i = 0;
        for (ViolationCount entry : counts) {
            String type;
            int period;
            int count;
            if (entry instanceof ViolationCount) {
                ViolationCount countEntry = (ViolationCount) entry;
                type = countEntry.getIllegalType().getDesc();
                period = countEntry.getIllegalType().getCode();
                count = countEntry.getCount();
            } else {
                continue;
            }
            QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("illegal_type", period);
            queryWrapper.between("create_time", startTime, endTime);
            queryWrapper.in("disposal_status", 2, 3);
            Long sentDown = illegalRecordsMapper.selectCount(queryWrapper);//已下派
            QueryWrapper<IllegalRecords> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("illegal_type", period);
            queryWrapper.between("create_time", startTime, endTime);
            queryWrapper1.eq("disposal_status", 2);
            Long processed = illegalRecordsMapper.selectCount(queryWrapper1);//已处理
            double handleRate = 0.0;
            if (sentDown != 0) {
                handleRate = Double.parseDouble(String.format("%.2f", (double) processed / sentDown * 100));

            }
            i = i + 1;
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("id", i);
            objectObjectHashMap.put("name", type);      //类型
            objectObjectHashMap.put("data", count);     //违法数量
            objectObjectHashMap.put("send", sentDown);  //已下派
            objectObjectHashMap.put("handled", processed);//已处理
            objectObjectHashMap.put("handleRate", handleRate);  //处理率
            list.add(objectObjectHashMap);
        }
        return SaResult.data(list);
    }

    private static @NotNull QueryWrapper<IllegalRecords> getIllegalRecordsQueryWrapper(SelectIllegalRecords selectIllegalRecords) {
        QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
        // 根据 involvePlate 参数添加 plate_number 的查询条件

        if (selectIllegalRecords.getInvolvePlate() != null) {
            if (selectIllegalRecords.getInvolvePlate() == 0) {
                // 当 involvePlate 为 0 时，查找 plate_number 为空或 "" 的记录
                queryWrapper.and(wrapper -> wrapper.isNull("plate_number").or().eq("plate_number", ""));
            } else if (selectIllegalRecords.getInvolvePlate() == 1) {
                // 当 involvePlate 为 1 时，查找 plate_number 非空且不为 "" 的记录，并且如果提供了 plateNumber，则模糊匹配
                queryWrapper.and(wrapper -> wrapper.isNotNull("plate_number").and(wrap -> wrap.ne("plate_number", "")));
                if (StringUtils.isNotBlank(selectIllegalRecords.getPlateNumber())) {
                    queryWrapper.like("plate_number", selectIllegalRecords.getPlateNumber());
                }
            }
        }
//        } else {
//            queryWrapper.isNotNull("plate_number").and(wrapper -> wrapper.ne("plate_number", ""));
//
        queryWrapper.eq(selectIllegalRecords.getWhetherToModify() != null, "whether_to_modify", selectIllegalRecords.getWhetherToModify());
        queryWrapper.eq(selectIllegalRecords.getDisposalStatus() != null, "disposal_status", selectIllegalRecords.getDisposalStatus());
        queryWrapper.eq(selectIllegalRecords.getVehicleType() != null, "vehicle_type", selectIllegalRecords.getVehicleType());
        queryWrapper.eq(selectIllegalRecords.getPersuasiveBehavior() != null, "persuasive_behavior", selectIllegalRecords.getPersuasiveBehavior());
        //queryWrapper.eq(selectIllegalRecords.getInvolvePlate() != null, "involve_plate", selectIllegalRecords.getInvolvePlate());
        queryWrapper.ge(selectIllegalRecords.getNumberOfPassengers() != null, "number_of_passengers", selectIllegalRecords.getNumberOfPassengers());
        //queryWrapper.eq(selectIllegalRecords.getHandMovement() != null, "hand_movement", selectIllegalRecords.getHandMovement());
        queryWrapper.in("illegal_type", 1, 2, 3, 5, 6, 7, 8);
        queryWrapper.eq(StringUtils.isNotBlank(selectIllegalRecords.getCity()), "city", selectIllegalRecords.getCity());
        queryWrapper.eq(StringUtils.isNotBlank(selectIllegalRecords.getCounty()), "county", selectIllegalRecords.getCounty());
        queryWrapper.eq(StringUtils.isNotBlank(selectIllegalRecords.getTownship()), "township", selectIllegalRecords.getTownship());
        queryWrapper.eq(StringUtils.isNotBlank(selectIllegalRecords.getHamlet()), "hamlet", selectIllegalRecords.getHamlet());
        queryWrapper.eq(StringUtils.isNotBlank(selectIllegalRecords.getSite()), "site", selectIllegalRecords.getSite());
        queryWrapper.eq(selectIllegalRecords.getIllegalType() != null, "illegal_type", selectIllegalRecords.getIllegalType());
        queryWrapper.between(selectIllegalRecords.getStartTime() != null && selectIllegalRecords.getEndTime() != null, "capture_time", selectIllegalRecords.getStartTime(), selectIllegalRecords.getEndTime());
        return queryWrapper;
    }

    private TreeNode insertIfNotExists(String label, int parentId, int level) {
        if (label == null || label.isEmpty()) {
            return null; // 或者你可以选择抛出异常，或者返回一个默认值
        }
        QueryWrapper<TreeNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("label", label);
        queryWrapper.eq("parentId", parentId);
        TreeNode node = treeNodeMapper.selectOne(queryWrapper);
        if (node == null) {
            node = new TreeNode();
            node.setLabel(label);
            node.setLevel(level);
            node.setParentId(parentId);
            treeNodeMapper.insert(node);
        }
        return node;
    }

    @Override
    public SaResult getTopLocationsByYear(int year) {
        List<Map<String, Object>> topLocations = illegalRecordsMapper.selectTopLocationsByYear(year);
        return SaResult.data(topLocations);
    }

    @Override
    public SaResult getTopLocationsByMonth(int year, int month) {
        List<Map<String, Object>> topLocations = illegalRecordsMapper.selectTopLocationsByMonth(year, month);
        return SaResult.data(topLocations);
    }

    @Override
    public SaResult getTopLocationsByDay(int year, int month, int day) {
        List<Map<String, Object>> topLocations = illegalRecordsMapper.selectTopLocationsByDay(year, month, day);
        return SaResult.data(topLocations);
    }

    @Override
    public SaResult getTopLocationsByWeek(int year, int week) {
        List<Map<String, Object>> topLocations = illegalRecordsMapper.selectTopLocationsByWeek(year, week);
        return SaResult.data(topLocations);
    }

    @Override
    public SaResult getTopLocationsByLastSevenDays() {
        List<Map<String, Object>> topLocations = illegalRecordsMapper.selectTopLocationsByLastSevenDays();
        return SaResult.data(topLocations);
    }

    /**
     * 格式化数据为 ECharts 所需的格式
     */
    private Map<String, Object> formatForECharts(List<?> data, int periods, String periodType, Date startTime, Date endTime) {
        Map<String, Object> result = new HashMap<>();
        List<String> categories = new ArrayList<>();
        List<Map<String, Object>> series = new ArrayList<>();

        // 初始化数据结构，为每种违法类型创建一个包含periods个0的列表
        Map<String, List<Integer>> dataMap = new HashMap<>();

        // 处理每条记录
        for (Object entry : data) {
            String type;
            int period;
            int count;

            if (entry instanceof ViolationTrend) {
                ViolationTrend trend = (ViolationTrend) entry;
                type = trend.getIllegalType().getDesc();
                period = trend.getPeriod();
                count = trend.getCount();

                // 根据不同的周期类型处理 period
                switch (periodType) {
                    case "month":  // MONTH返回1-12
                        period = period - 1;
                        break;
                    case "day":  // DAY返回1-31
                        period = period - 1;
                        break;
                    // hour不需要处理，因为HOUR返回0-23
                }
            } else if (entry instanceof ViolationCount) {
                ViolationCount countEntry = (ViolationCount) entry;
                type = countEntry.getIllegalType().getDesc();
                period = 0;
                count = countEntry.getCount();
            } else {
                continue;
            }

            dataMap.putIfAbsent(type, new ArrayList<>(Collections.nCopies(periods, 0)));

            // 确保period在有效范围内
            if (period >= 0 && period < periods) {
                dataMap.get(type).set(period, count);
            }
        }

        // 构建返回数据
        for (Map.Entry<String, List<Integer>> entry : dataMap.entrySet()) {
            Map<String, Object> seriesData = new HashMap<>();
            seriesData.put("name", entry.getKey());
            seriesData.put("data", entry.getValue());
            series.add(seriesData);
        }

        // 生成日期范围（用于周统计）
        List<DateTime> dateRange = null;
        if (startTime != null && endTime != null) {
            dateRange = DateUtil.rangeToList(startTime, endTime, DateField.DAY_OF_YEAR);
        }

        // 设置类别（如小时、天、月）
        for (int i = 0; i < periods; i++) {
            switch (periodType) {
                case "week":
                    if (dateRange != null && i < dateRange.size()) {
                        categories.add(DateUtil.format(dateRange.get(i), "MM-dd"));
                    }
                    break;
                case "hour":
                    categories.add(String.format("%02d:00", i));
                    break;
                case "day":
                    categories.add(i + 1 + "号");
                    break;
                case "month":
                    categories.add(i + 1 + "月");
                    break;
                default:
                    categories.add(String.valueOf(i));
            }
        }

        result.put("categories", categories);
        result.put("series", series);
        return result;
    }

    @Override
    public Map<String, Object> getWeeklyStatisticsForECharts() {
        Date today = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.DAY_OF_YEAR, -6);
        Date sevenDaysAgo = calendar.getTime();
        // 获取近七天的违法总数
        List<Map<String, Object>> weeklyTotalIllegalCounts = getIllegalCountsByDateRange(sevenDaysAgo, today);
        // 获取近七天的下派数量
        List<Map<String, Object>> weeklyDispatchedCounts = getDispatchedCountsByDateRange(sevenDaysAgo, today);
        // 计算下派率
        List<Double> weeklyDispatchRates = calculateDispatchRates(weeklyTotalIllegalCounts, weeklyDispatchedCounts, sevenDaysAgo, today);
        // 格式化数据以适合 ECharts
        return formatStatisticsForECharts(weeklyTotalIllegalCounts, weeklyDispatchedCounts, weeklyDispatchRates, sevenDaysAgo, today);
    }

    public List<Map<String, Object>> getIllegalCountsByDateRange(Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startDateStr = sdf.format(startDate);
        String endDateStr = sdf.format(endDate);
        // 使用格式化后的日期字符串进行查询
        List<Map<String, Object>> results = illegalRecordsMapper.selectIllegalCountsByDateRange(startDateStr, endDateStr);
        return results;
    }

    private List<Map<String, Object>> getDispatchedCountsByDateRange(Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startDateStr = sdf.format(startDate);
        String endDateStr = sdf.format(endDate);
        return illegalRecordsMapper.selectDispatchedCountsByDateRange(startDateStr, endDateStr);
    }

    private List<Double> calculateDispatchRates(List<Map<String, Object>> totalIllegalCounts, List<Map<String, Object>> dispatchedCounts, Date startDate, Date endDate) {
        Map<String, Integer> totalIllegalMap = totalIllegalCounts.stream()
                .collect(Collectors.toMap(entry -> entry.get("date").toString().substring(5), entry -> ((Number) entry.get("count")).intValue()));
        Map<String, Integer> dispatchedMap = dispatchedCounts.stream()
                .collect(Collectors.toMap(entry -> entry.get("date").toString().substring(5), entry -> ((Number) entry.get("count")).intValue()));
        List<String> dateCategories = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        while (!calendar.getTime().after(endDate)) {
            dateCategories.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        List<Double> dispatchRates = new ArrayList<>(Collections.nCopies(dateCategories.size(), 0.0)); // 初始化为0
        for (int i = 0; i < dateCategories.size(); i++) {
            String date = dateCategories.get(i);
            int total = totalIllegalMap.getOrDefault(date, 0);
            int dispatched = dispatchedMap.getOrDefault(date, 0);
            double rate = (total == 0) ? 0.0 : ((double) dispatched / total) * 100;
            dispatchRates.set(i, Math.round(rate * 10) / 10.0); // 保留一位小数
        }
        return dispatchRates;
    }

    private Map<String, Object> formatStatisticsForECharts(List<Map<String, Object>> totalIllegalCounts, List<Map<String, Object>> dispatchedCounts,
                                                           List<Double> dispatchRates, Date startDate, Date endDate) {
        Map<String, Object> result = new HashMap<>();
        List<String> dateCategories = new ArrayList<>();
        List<Integer> totalIllegalData = new ArrayList<>();
        List<Integer> dispatchedData = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        while (!calendar.getTime().after(endDate)) {
            dateCategories.add(sdf.format(calendar.getTime()));
            totalIllegalData.add(0); // 初始化为0
            dispatchedData.add(0); // 初始化为0
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        // 填充数据
        fillData(totalIllegalCounts, totalIllegalData, dateCategories);
        fillData(dispatchedCounts, dispatchedData, dateCategories);
        ArrayList<Object> objects = new ArrayList<>();
        for (int i = 0; i < dateCategories.size(); i++) {
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("date", dateCategories.get(i));
            objectObjectHashMap.put("value", totalIllegalData.get(i));
            objects.add(objectObjectHashMap);
        }
        result.put("categories", objects);   // 日期列表
        result.put("totalIllegalData", totalIllegalData.get(totalIllegalData.size() - 1)); // 违法总数
        result.put("dispatchedData", dispatchedData.get(dispatchedData.size() - 1));   // 下派数量
        result.put("dispatchRates", dispatchRates.get(dispatchedData.size() - 1));   // 下派率

        return result;
    }

    private void fillData(List<Map<String, Object>> data, List<Integer> targetData, List<String> dateCategories) {
        for (Map<String, Object> entry : data) {
            String date = entry.get("date").toString();
            int count = ((Number) entry.get("count")).intValue();
            int index = dateCategories.indexOf(date.substring(5)); // 只取月-日部分
            if (index != -1) {
                targetData.set(index, count);
            }
        }
    }


    @Override
    public SaResult getViolationTypeAnalysis(String city, String county, String township, String hamlet, String site) {
        List<Map<String, Integer>> violationTypeCounts = illegalRecordsMapper.getViolationTypeCountsForToday(city, county, township, hamlet, site);
        ArrayList<Object> objects = new ArrayList<>();
        // 计算总数
        int total = 0;
        for (Map<String, Integer> entry : violationTypeCounts) {
            int count = ((Number) entry.get("count")).intValue();
            total += count;
        }
        for (Map<String, Integer> entry : violationTypeCounts) {
            Map<String, Object> result = new HashMap<>();
            String typeName = String.valueOf(TrafficEnum.of(entry.get("type")).getDesc()); // 使用 TrafficEnum 进行转换
            int count = ((Number) entry.get("count")).intValue();
            result.put("type", typeName);
            result.put("value", count);
            result.put("percent", String.format("%.2f", count * 100.0 / total));
            objects.add(result);
        }
        return SaResult.data(objects);
    }
    @Override
    public SaResult topLocationsByDay(String city, String county, String township, String hamlet, String site, Date date) {
        int year = DateUtil.year(date);
        int month = DateUtil.month(date)+1;
        int day = DateUtil.dayOfMonth(date);
        String groupByLevel = getTheNextLevel(city, county, township, hamlet, site);
        List<Map<String, Object>> topLocations = illegalRecordsMapper.topLocationsByDay(year, month, day, city, county, township, hamlet, site, groupByLevel);
        return SaResult.data(topLocations);
    }

    @Override
    public SaResult topLocationsByMonth(String city, String county, String township, String hamlet, String site, Date date) {
        int year = DateUtil.year(date);
        int month = DateUtil.month(date)+1;
        String groupByLevel = getTheNextLevel(city, county, township, hamlet, site);
        List<Map<String, Object>> topLocations = illegalRecordsMapper.topLocationsByMonth(year, month, city, county, township, hamlet, site, groupByLevel);
        return SaResult.data(topLocations);
    }

    public String getTheNextLevel(String city, String county, String township, String hamlet, String site) {
        if (StringUtils.isNotBlank(site)) {
            return "site";
        } else if (StringUtils.isNotBlank(hamlet)) {
            return "site";
        } else if (StringUtils.isNotBlank(township)) {
            return "hamlet";
        } else if (StringUtils.isNotBlank(county)) {
            return "township";
        } else if (StringUtils.isNotBlank(city)) {
            return "county";
        }
        // 默认返回空字符串（理论上不会到达这里）
        return "";
    }

    @Override
    public SaResult DayStatistics(String city, String county, String township, String hamlet, String site, Date date) {
        int total = illegalRecordsMapper.getTotalCountForToday(city, county, township, hamlet, site,date);   //今日违法总数
        int handled = illegalRecordsMapper.getHandledCountForToday(city, county, township, hamlet, site,date);      //下派总数
        // 获取统计数据（包含详情）
        List<Map<String, Object>> statistics = illegalRecordsMapper.getDayStatisticsWithDetails(date, city, county, township, hamlet, site);
        // 创建一个包含所有小时的Map
        Map<String, Map<String, Object>> hourlyData = new HashMap<>();
        // 初始化24小时的数据
        for (int i = 0; i < 24; i++) {
            Map<String, Object> hourData = new HashMap<>();
            String hour = String.format("%02d", i);
            hourData.put("date", hour + ":00");
            hourData.put("value", 0);
            hourData.put("details", new ArrayList<>()); // 改为空数组
            hourlyData.put(hour, hourData);
        }
        // 用实际数据更新Map
        statistics.stream().forEach(stat -> {
            String hour = String.format("%02d", Integer.parseInt(stat.get("hour").toString()));
            Number countNum = (Number) stat.get("count");
            int count = countNum.intValue();
            String detailsStr = (String) stat.get("details");

            Map<String, Object> hourData = hourlyData.get(hour);
            hourData.put("value", count);
            if (detailsStr != null) {
                // 解析JSON数组
                JSONArray detailsArray = JSON.parseArray(detailsStr);
                List<Map<String, Object>> formattedDetails = new ArrayList<>();

                for (int i = 0; i < detailsArray.size(); i++) {
                    JSONObject detail = detailsArray.getJSONObject(i);
                    int typeCode = detail.getIntValue("type");
                    int typeCount = detail.getIntValue("count");

                    TrafficEnum trafficEnum = TrafficEnum.of(typeCode);
                    Map<String, Object> formattedDetail = new HashMap<>();
                    formattedDetail.put("name", trafficEnum.getDesc());
                    formattedDetail.put("value", typeCount);
                    formattedDetail.put("itemStyle", new HashMap<String, String>() {{
                        put("color", trafficEnum.getColor());
                    }});

                    formattedDetails.add(formattedDetail);
                }
                hourData.put("details", formattedDetails);
            }
        });

        // 转换为List并保持顺序
        List<Map<String, Object>> trend = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            String hour = String.format("%02d", i);
            trend.add(hourlyData.get(hour));
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("handled", handled);
        result.put("trend", trend);

        return SaResult.data(result);
    }

    @Override
    public SaResult MonthStatistics(String city, String county, String township, String hamlet, String site, Date date) {
        int total = illegalRecordsMapper.getTotalCountForMonth(city, county, township, hamlet, site,date);   //当月违法总数
        int handled = illegalRecordsMapper.getHandledCountForMonth(city, county, township, hamlet, site,date);    //当月违法总数
        // 获取统计数据（包含详情）
        List<Map<String, Object>> statistics = illegalRecordsMapper.selectMonthStatisticsWithDetails(date, city, county, township, hamlet, site);
        // 获取当月天数
        YearMonth yearMonth = YearMonth.of(DateUtil.year(date), DateUtil.month(date)+1);
        int daysInMonth = yearMonth.lengthOfMonth();

        // 创建一个包含所有天的Map
        Map<String, Map<String, Object>> dailyData = new HashMap<>();
        // 初始化当月所有天的数据
        for (int i = 1; i <= daysInMonth; i++) {
            Map<String, Object> dayData = new HashMap<>();
            String day = String.format("%02d", i);
            dayData.put("date", day);
            dayData.put("value", 0);
            dayData.put("details", new ArrayList<>()); // 空数组
            dailyData.put(day, dayData);
        }

        // 用实际数据更新Map
        statistics.stream().forEach(stat -> {
            String day = String.format("%02d", Integer.parseInt(stat.get("day").toString()));
            Number countNum = (Number) stat.get("count");
            int count = countNum.intValue();
            String detailsStr = (String) stat.get("details");

            Map<String, Object> dayData = dailyData.get(day);
            dayData.put("value", count);
            if (detailsStr != null) {
                // 解析JSON数组
                JSONArray detailsArray = JSON.parseArray(detailsStr);
                List<Map<String, Object>> formattedDetails = new ArrayList<>();

                for (int i = 0; i < detailsArray.size(); i++) {
                    JSONObject detail = detailsArray.getJSONObject(i);
                    int typeCode = detail.getIntValue("type");
                    int typeCount = detail.getIntValue("count");

                    TrafficEnum trafficEnum = TrafficEnum.of(typeCode);
                    Map<String, Object> formattedDetail = new HashMap<>();
                    formattedDetail.put("name", trafficEnum.getDesc());
                    formattedDetail.put("value", typeCount);
                    formattedDetail.put("itemStyle", new HashMap<String, String>() {{
                        put("color", trafficEnum.getColor());
                    }});

                    formattedDetails.add(formattedDetail);
                }
                dayData.put("details", formattedDetails);
            }
        });

        // 转换为List并保持顺序
        List<Map<String, Object>> trend = new ArrayList<>();
        for (int i = 1; i <= daysInMonth; i++) {
            String day = String.format("%02d", i);
            trend.add(dailyData.get(day));
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("handled", handled);
        result.put("trend", trend);

        return SaResult.data(result);
    }

    @Override
    public SaResult getPointDetailDataByDay(String city, String county, String township, String hamlet, String site, int year, int month, int day) {
        List<Map<String, Object>> pointDetails = illegalRecordsMapper.getPointDetailDataByDay(year, month, day, city, county, township, hamlet, site);
        return processPointDetails(pointDetails);
    }

    @Override
    public SaResult getPointDetailDataByMonth(String city, String county, String township, String hamlet, String site, int year, int month) {
        List<Map<String, Object>> pointDetails = illegalRecordsMapper.getPointDetailDataByMonth(year, month, city, county, township, hamlet, site);
        return processPointDetails(pointDetails);
    }

    @Override
    public SaResult getPointDetailDataByYear(String city, String county, String township, String hamlet, String site, int year) {
        List<Map<String, Object>> pointDetails = illegalRecordsMapper.getPointDetailDataByYear(year, city, county, township, hamlet, site);
        return processPointDetails(pointDetails);
    }

    @Override
    public SaResult getViolationTypeStatistics(String city, String county, String township, String hamlet, String site, Date date) {
        List<Map<String, Object>> statistics = illegalRecordsMapper.getViolationTypeStatisticsToday(city, county, township, hamlet, site,date);
        // 转换数据格式
        List<Map<String, Object>> result = statistics.stream()
                .map(stat -> {
                    Map<String, Object> item = new HashMap<>();
                    int typeCode = ((Number) stat.get("type")).intValue();
                    int count = ((Number) stat.get("count")).intValue();

                    TrafficEnum trafficEnum = TrafficEnum.of(typeCode);
                    item.put("name", trafficEnum.getDesc());
                    item.put("value", count);
                    return item;
                })
                .collect(Collectors.toList());
        return SaResult.data(result);
    }

    /**
     * 处理点位详情数据，计算总违法数、总下派数、总劝导数，并生成每种违法类型的详细信息。
     *
     * @param pointDetails 包含每种违法类型的统计数据列表
     * @return 包含总计和每种违法类型详细信息的 SaResult 对象
     */
    private SaResult processPointDetails(List<Map<String, Object>> pointDetails) {
        int totalViolations = 0;  // 总违法数
        int totalDispatched = 0;  // 总下派数
        int totalPersuaded = 0;   // 总劝导数
        List<Map<String, Object>> typeDetails = new ArrayList<>();  // 每种违法类型的详细信息列表
        for (Map<String, Object> detail : pointDetails) {
            int violations = ((Number) detail.get("total_violations")).intValue();  // 当前类型的违法总数
            int dispatched = ((Number) detail.get("dispatched_count")).intValue();  // 当前类型的下派次数
            int persuaded = ((Number) detail.get("persuaded_count")).intValue();    // 当前类型的劝导次数
            double dispatchRate = dispatched > 0 ? (double) persuaded / dispatched * 100 : 0;  // 当前类型的下派率
            totalViolations += violations;  // 累加总违法数
            totalDispatched += dispatched;  // 累加总下派数
            totalPersuaded += persuaded;    // 累加总劝导数
            Map<String, Object> typeDetail = new HashMap<>();
            Object illegalType = detail.get("illegal_type");
            if (illegalType instanceof Integer) {
                typeDetail.put("type", TrafficEnum.of((Integer) illegalType).getDesc());
            } else if (illegalType instanceof Long) {
                typeDetail.put("type", TrafficEnum.of(((Long) illegalType).intValue()).getDesc());
            }// 违法类型描述
            typeDetail.put("violations", violations);  // 违法总数
            typeDetail.put("dispatched", dispatched);  // 下派次数
            typeDetail.put("persuaded", persuaded);    // 劝导次数
            typeDetail.put("dispatchRate", String.format("%.2f", dispatchRate));  // 下派率
            typeDetails.add(typeDetail);  // 添加到详细信息列表
        }
        Map<String, Object> result = new HashMap<>();
        result.put("totalViolations", totalViolations);  // 总违法数
        result.put("totalDispatched", totalDispatched);  // 总下派数
        result.put("totalPersuaded", totalPersuaded);    // 总劝导数
        result.put("typeDetails", typeDetails);          // 每种违法类型的详细信息
        return SaResult.data(result);
    }

    @Override
    public SaResult getPersuasionStatsByWorkTime(String city, String county, String township,
                                                 String hamlet, String site, LocalDate startDate, LocalDate endDate) {
        try {
            // 获取工作时间段
            String theNextLevel = getTheNextLevel(city, county, township, hamlet, site);
            List<Map<String, Object>> rawWorkingHours = scheduleMapper.queryWorkingHours(city, county, township, hamlet, site, startDate, endDate, theNextLevel);
            // 按地点和日期组织工作时间数据，便于后续处理
            Map<String, Set<String>> workingHoursByLocationAndDate = organizeWorkingHoursByLocationAndDate(rawWorkingHours);
            //根据工作时间段进行去重和合并
            List<Map<String, Object>> workingHours = mergeAndRemoveDuplicates(rawWorkingHours);
            //根据工作时间找出对应日期
            if (workingHours.isEmpty()) {
                return SaResult.ok("未找到工作时间安排");
            }
            List<Map<String, Object>> allStats = new ArrayList<>();
            // 遍历每个工作时间段
            for (Map<String, Object> workHour : workingHours) {
                String date = workHour.get("schedule_date").toString();
                // 构建完整的时间
                LocalDate workDate = LocalDate.parse(date);
                LocalDateTime periodStart = LocalDateTime.of(workDate, LocalTime.parse(workHour.get("start_time").toString()));
                LocalDateTime periodEnd = LocalDateTime.of(workDate, LocalTime.parse(workHour.get("end_time").toString()));
                // 将 LocalDateTime 转换为 Date
                Date startTime = Date.from(periodStart.atZone(ZoneId.systemDefault()).toInstant());
                Date endTime = Date.from(periodEnd.atZone(ZoneId.systemDefault()).toInstant());
                //查询工作时间的所有违法数量
                Map<String, Object> stats = illegalRecordsMapper.exampleQueryWorkingHoursStatistics(
                        startTime, endTime, workHour.get("city").toString(), workHour.get("county").toString(),
                        workHour.get("township").toString(), workHour.get("hamlet").toString(), workHour.get("site").toString(), theNextLevel);
                allStats.add(stats);
            }
            List<Map<String, Object>> mergedStatsList = summarizeIllegalRecords(allStats, theNextLevel);
            // 存储每个地点的劝导统计汇总
            List<Map<String, Object>> persuasionStatsList = new ArrayList<>();
            for (Map.Entry<String, Set<String>> entry : workingHoursByLocationAndDate.entrySet()) {
                String location = entry.getKey();
                Set<String> dates = entry.getValue();
//                log.info("点位: {} 的排班日期如下:", location);
                // 解析地点信息
                String[] locationParts = location.split("_");
                Map<String, Object> locationStats = new HashMap<>();
                // 设置地点信息
                locationStats.put("city", locationParts[0]);
                locationStats.put("county", locationParts[1]);
                locationStats.put("township", locationParts[2]);
                locationStats.put("hamlet", locationParts[3]);
                locationStats.put("site", locationParts[4]);
                // 初始化统计数据
                locationStats.put("persuaded_count", 0);
                locationStats.put("effective_count", 0);
                for (String date : dates) {
                    //查询当天的劝导统计
                    Map<String, Object> persuadedStats = illegalRecordsMapper.getPersuadedCountByWorkingHours(
                            Date.from(LocalDate.parse(date).atStartOfDay(ZoneId.systemDefault()).toInstant()),
                            Date.from(LocalDate.parse(date).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant()),
                            locationStats.get("city").toString(),
                            locationStats.get("county").toString(),
                            locationStats.get("township").toString(),
                            locationStats.get("hamlet").toString(),
                            locationStats.get("site").toString(),
                            theNextLevel);
//                    log.info("查询{},{}时间段内劝导统计: {}", date, location, persuadedStats);
                    // 累加统计数据
                    if (persuadedStats != null) {
                        int persuadedCount = ((Number) locationStats.get("persuaded_count")).intValue() +
                                ((Number) persuadedStats.getOrDefault("persuaded_count", 0)).intValue();
                        int effectiveCount = ((Number) locationStats.get("effective_count")).intValue() +
                                ((Number) persuadedStats.getOrDefault("effective_count", 0)).intValue();
                        locationStats.put("persuaded_count", persuadedCount);
                        locationStats.put("effective_count", effectiveCount);
                    }
                }
                // 添加到列表中
                persuasionStatsList.add(locationStats);
            }

            // 合并违法统计和劝导统计数据
            for (Map<String, Object> illegalStat : mergedStatsList) {
                String illegalKey = buildLocationKeyByLevel(illegalStat, theNextLevel);
                int totalPersuadedCount = 0;
                int totalEffectiveCount = 0;

                // 查找所有匹配的劝导统计数据
                for (Map<String, Object> persuasionStat : persuasionStatsList) {
                    String persuasionKey = buildLocationKeyByLevel(persuasionStat, theNextLevel);

                    if (illegalKey.equals(persuasionKey)) {
                        // 累加劝导统计数据
                        totalPersuadedCount += ((Number) persuasionStat.get("persuaded_count")).intValue();
                        totalEffectiveCount += ((Number) persuasionStat.get("effective_count")).intValue();
                    }
                }

                // 设置汇总后的劝导统计数据
                illegalStat.put("persuaded_count", totalPersuadedCount);
                illegalStat.put("effective_count", totalEffectiveCount);

                // 计算劝导率
                int totalCount = ((Number) illegalStat.get("count")).intValue();
                double persuasionRate = totalCount > 0 ? (totalPersuadedCount * 100.0 / totalCount) : 0;
                illegalStat.put("persuasion_rate", String.format("%.2f", persuasionRate));

                illegalStat.put("display", illegalStat.get(theNextLevel));
            }

//            log.info("最终合并后的统计数据: {}", mergedStatsList);
            return SaResult.data(mergedStatsList);

        } catch (Exception e) {
            log.error("查询工作时间内劝导统计失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 汇总违法数据
     */
    private List<Map<String, Object>> summarizeIllegalRecords(List<Map<String, Object>> allStats, String theNextLevel) {
        // 根据 theNextLevel 合并统计数据
        Map<String, Map<String, Object>> mergedStats = new HashMap<>();
        for (Map<String, Object> stat : allStats) {
            if (stat == null) {
                continue;
            }
            // 根据不同级别检查必要的字段
            boolean isValid = switch (theNextLevel) {
                case "site" -> stat.get("city") != null && stat.get("county") != null &&
                        stat.get("township") != null && stat.get("hamlet") != null &&
                        stat.get("site") != null;
                case "hamlet" -> stat.get("city") != null && stat.get("county") != null &&
                        stat.get("township") != null && stat.get("hamlet") != null;
                case "township" -> stat.get("city") != null && stat.get("county") != null &&
                        stat.get("township") != null;
                case "county" -> stat.get("city") != null && stat.get("county") != null;
                default -> stat.get("city") != null;
            };

            if (!isValid) {
                continue;
            }
            // 根据 theNextLevel 构建 key
            String locationKey = switch (theNextLevel) {
                case "site" -> String.format("%s-%s-%s-%s-%s",
                        stat.get("city"), stat.get("county"), stat.get("township"),
                        stat.get("hamlet"), stat.get("site"));
                case "hamlet" -> String.format("%s-%s-%s-%s",
                        stat.get("city"), stat.get("county"), stat.get("township"),
                        stat.get("hamlet"));
                case "township" -> String.format("%s-%s-%s",
                        stat.get("city"), stat.get("county"), stat.get("township"));
                case "county" -> String.format("%s-%s",
                        stat.get("city"), stat.get("county"));
                default -> (String) stat.get("city");
            };
            // 合并数据
            mergedStats.compute(locationKey, (k, v) -> {
                if (v == null) {
                    // 第一次遇到这个地点，创建新的统计数据
                    Map<String, Object> newStat = new HashMap<>();
                    // 保存地点信息
                    newStat.put("city", stat.get("city"));
                    newStat.put("county", stat.get("county"));
                    newStat.put("township", stat.get("township"));
                    newStat.put("hamlet", stat.get("hamlet"));
                    newStat.put("site", stat.get("site"));
                    // 初始化统计数据
                    newStat.put("count", stat.get("count"));
                    return newStat;
                } else {
                    // 只合并统计数值，保持原有的地点信息
                    if (stat.get("count") != null) {
                        int existingCount = ((Number) v.get("count")).intValue();
                        int newCount = ((Number) stat.get("count")).intValue();
                        v.put("count", existingCount + newCount);
                    }
                    return v;
                }
            });
        }

        // 转换回列表形式
        List<Map<String, Object>> mergedStatsList = new ArrayList<>(mergedStats.values());
        return mergedStatsList;
    }

    /**
     * 将工作时间数据按地点和日期进行组织
     *
     * @param rawWorkingHours 原始工作时间数据
     * @return 组织后的数据，结构为：地点 -> 日期列表
     */
    private Map<String, Set<String>> organizeWorkingHoursByLocationAndDate(List<Map<String, Object>> rawWorkingHours) {
        Map<String, Set<String>> result = new HashMap<>();

        for (Map<String, Object> workHour : rawWorkingHours) {
            // 构建地点键
            String locationKey = buildLocationKey(workHour);
            // 获取日期
            String dateKey = workHour.get("schedule_date").toString();

            // 将日期添加到对应地点的集合中
            result.computeIfAbsent(locationKey, k -> new HashSet<>())
                    .add(dateKey);
        }

        return result;
    }

    //整合时间段并移除重复
    private List<Map<String, Object>> mergeAndRemoveDuplicates(List<Map<String, Object>> rawWorkingHours) {
        // 按照点位和日期分组
        Map<String, Map<String, List<Map<String, Object>>>> groupedByLocationAndDate = new HashMap<>();

        for (Map<String, Object> workHour : rawWorkingHours) {
            String locationKey = buildLocationKey(workHour);
            String dateKey = workHour.get("schedule_date").toString();
            // 初始化嵌套Map
            groupedByLocationAndDate
                    .computeIfAbsent(locationKey, k -> new HashMap<>())
                    .computeIfAbsent(dateKey, k -> new ArrayList<>())
                    .add(workHour);
        }

        // 整合时间段并移除重复，保留点位和日期信息
        List<Map<String, Object>> workingHours = new ArrayList<>();

        // 遍历每个点位
        for (Map.Entry<String, Map<String, List<Map<String, Object>>>> locationEntry : groupedByLocationAndDate.entrySet()) {
            // String locationKey = locationEntry.getKey();
            Map<String, List<Map<String, Object>>> dateMap = locationEntry.getValue();

            // 遍历每个日期
            for (Map.Entry<String, List<Map<String, Object>>> dateEntry : dateMap.entrySet()) {
                String dateKey = dateEntry.getKey();
                List<Map<String, Object>> timeSlots = dateEntry.getValue();

                // 按开始时间排序
                timeSlots.sort((a, b) -> {
                    LocalTime timeA = LocalTime.parse(a.get("start_time").toString());
                    LocalTime timeB = LocalTime.parse(b.get("start_time").toString());
                    return timeA.compareTo(timeB);
                });

                // 合并重叠的时间段
                List<Map<String, Object>> mergedSlots = new ArrayList<>();
                if (!timeSlots.isEmpty()) {
                    Map<String, Object> current = new HashMap<>(timeSlots.get(0));
                    LocalTime currentEnd = LocalTime.parse(current.get("end_time").toString());

                    for (int i = 1; i < timeSlots.size(); i++) {
                        Map<String, Object> next = timeSlots.get(i);
                        LocalTime nextStart = LocalTime.parse(next.get("start_time").toString());
                        LocalTime nextEnd = LocalTime.parse(next.get("end_time").toString());

                        // 如果时间段重叠或相邻，合并它们
                        if (nextStart.compareTo(currentEnd) <= 0) {
                            if (nextEnd.compareTo(currentEnd) > 0) {
                                current.put("end_time", next.get("end_time"));
                                currentEnd = nextEnd;
                            }
                        } else {
                            // 不重叠，添加当前时间段并开始新的时间段
                            mergedSlots.add(current);
                            current = new HashMap<>(next);
                            currentEnd = nextEnd;
                        }
                    }

                    // 添加最后一个时间段
                    mergedSlots.add(current);
                }

                // 将合并后的时间段添加到结果中，并确保每个时间段都包含点位和日期信息
                for (Map<String, Object> slot : mergedSlots) {
                    // 确保每个合并后的时间段都包含原始的点位和日期信息
                    Map<String, Object> firstSlot = timeSlots.get(0);
                    slot.put("city", firstSlot.get("city"));
                    slot.put("county", firstSlot.get("county"));
                    slot.put("township", firstSlot.get("township"));
                    slot.put("hamlet", firstSlot.get("hamlet"));
                    slot.put("site", firstSlot.get("site"));
                    slot.put("schedule_date", dateKey);
                    workingHours.add(slot);
                }
            }
        }

        return workingHours;
    }

    @Override
    public SaResult getLocationTrends(String city, String county, String township,
                                      String hamlet, String site, LocalDate startDate, LocalDate endDate) {
        try {
            // 将 LocalDate 转换为 Date，并设置时间为当天开始和结束
            Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date end = Date.from(endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());

            // 从数据库查询趋势统计数据
            List<Map<String, Object>> stats = illegalRecordsMapper.getViolationTrendStats(
                    start, end, city, county, township, hamlet, site);

            // 按日期分组，将相同日期的所有违法记录放在一起
            Map<String, List<Map<String, Object>>> dailyStats = stats.stream()
                    .collect(Collectors.groupingBy(stat -> stat.get("date").toString()));

            // 存储每天的趋势数据（只返回前端需要的5个字段）
            List<Map<String, Object>> trends = new ArrayList<>();

            // 生成完整的日期范围，确保没有数据的日期也会显示为0
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                String dateStr = currentDate.toString();
                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", dateStr);  // 1. 日期字段

                // 检查当天是否有数据
                if (dailyStats.containsKey(dateStr)) {
                    List<Map<String, Object>> dayStats = dailyStats.get(dateStr);

                    // 计算当天的汇总统计
                    int totalViolations = dayStats.stream()
                            .mapToInt(s -> ((Number) s.get("totalCount")).intValue())
                            .sum();
                    int totalPersuaded = dayStats.stream()
                            .mapToInt(s -> ((Number) s.get("persuadedCount")).intValue())
                            .sum();
                    int effectiveCount = dayStats.stream()
                            .mapToInt(s -> ((Number) s.get("effectiveCount")).intValue())
                            .sum();

                    dayData.put("totalViolations", totalViolations);  // 2. 违法总数
                    dayData.put("totalPersuaded", totalPersuaded);    // 3. 已劝导数量
                    dayData.put("effectiveCount", effectiveCount);    // 4. 有效劝导数量
                    dayData.put("persuasionRate", calculatePersuasionRate(totalViolations, totalPersuaded));  // 5. 劝导率
                } else {
                    // 没有数据的日期填充0
                    dayData.put("totalViolations", 0);
                    dayData.put("totalPersuaded", 0);
                    dayData.put("effectiveCount", 0);
                    dayData.put("persuasionRate", "0.00");
                }

                trends.add(dayData);
                currentDate = currentDate.plusDays(1);
            }

            return SaResult.data(trends);
        } catch (Exception e) {
            log.error("查询点位违法趋势统计失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public SaResult getLocationTrendsByYear(int year, String city, String county, String township,
                                            String hamlet, String site) {
        try {
            // 从数据库查询年度趋势统计数据
            List<Map<String, Object>> stats = illegalRecordsMapper.getLocationTrendStatsByYear(
                    year, city, county, township, hamlet, site);

            // 按月份分组，将相同月份的所有违法记录放在一起
            Map<String, List<Map<String, Object>>> monthlyStats = stats.stream()
                    .collect(Collectors.groupingBy(stat -> stat.get("date").toString()));

            // 存储每月的趋势数据（只返回前端需要的5个字段）
            List<Map<String, Object>> trends = new ArrayList<>();

            // 生成完整的12个月，确保没有数据的月份也会显示为0
            for (int month = 1; month <= 12; month++) {
                String monthStr = String.format("%d-%02d", year, month);
                Map<String, Object> monthData = new HashMap<>();
                monthData.put("date", monthStr);  // 1. 日期字段（月份格式: YYYY-MM）

                // 检查当月是否有数据
                if (monthlyStats.containsKey(monthStr)) {
                    List<Map<String, Object>> monthStats = monthlyStats.get(monthStr);

                    // 计算当月的汇总统计
                    int totalViolations = monthStats.stream()
                            .mapToInt(s -> ((Number) s.get("totalCount")).intValue())
                            .sum();
                    int totalPersuaded = monthStats.stream()
                            .mapToInt(s -> ((Number) s.get("persuadedCount")).intValue())
                            .sum();
                    int effectiveCount = monthStats.stream()
                            .mapToInt(s -> ((Number) s.get("effectiveCount")).intValue())
                            .sum();

                    monthData.put("totalViolations", totalViolations);  // 2. 违法总数
                    monthData.put("totalPersuaded", totalPersuaded);    // 3. 已劝导数量
                    monthData.put("effectiveCount", effectiveCount);    // 4. 有效劝导数量
                    monthData.put("persuasionRate", calculatePersuasionRate(totalViolations, totalPersuaded));  // 5. 劝导率
                } else {
                    // 没有数据的月份填充0
                    monthData.put("totalViolations", 0);
                    monthData.put("totalPersuaded", 0);
                    monthData.put("effectiveCount", 0);
                    monthData.put("persuasionRate", "0.00");
                }

                trends.add(monthData);
            }

            return SaResult.data(trends);
        } catch (Exception e) {
            log.error("查询点位年度违法趋势统计失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 计算劝导率
     *
     * @param total     违法总数
     * @param persuaded 劝导成功数
     * @return 格式化的劝导率字符串，保留两位小数
     */
    private String calculatePersuasionRate(int total, int persuaded) {
        return total > 0 ? String.format("%.2f", (persuaded * 100.0 / total)) : "0.00";
    }


    @Override
    public SaResult getMapPointDetail(String city, String county, String township, String hamlet, String site) {
        try {
            QueryWrapper<Device> deviceQuery = new QueryWrapper<>();
            deviceQuery.eq(StringUtils.isNotBlank(city), "city", city);
            deviceQuery.eq(StringUtils.isNotBlank(county), "county", county);
            deviceQuery.eq(StringUtils.isNotBlank(township), "township", township);
            deviceQuery.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
            deviceQuery.eq(StringUtils.isNotBlank(site), "site", site);
            // 获取符合条件的所有设备
            List<Device> devices = deviceMapper.selectList(deviceQuery);
            if (devices.isEmpty()) {
                return SaResult.ok("未找到设备信息");
            }
            // 按经纬度分组设备
            Map<String, List<Device>> groupedDevices = devices.stream()
                    .collect(Collectors.groupingBy(device ->
                            device.getLongitude() + "," + device.getLatitude()));
            // 使用CompletableFuture并行处理每个点位数据
            List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
            for (List<Device> locationDevices : groupedDevices.values()) {
                CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                    Device firstDevice = locationDevices.get(0);
                    Map<String, Object> point = new HashMap<>();
                    Map<String, Object> data = new HashMap<>();
                    try {
                        // 设置基本信息
                        point.put("id", firstDevice.getId());
                        point.put("coordinates", new String[]{firstDevice.getLongitude(), firstDevice.getLatitude()});
                        data.put("address", buildAddress(firstDevice));
                        data.put("city", firstDevice.getCity());
                        data.put("county", firstDevice.getCounty());
                        data.put("township", firstDevice.getTownship());
                        data.put("hamlet", firstDevice.getHamlet());
                        data.put("site", firstDevice.getSite());
                        // 设置该点位的所有设备信息
                        List<Map<String, Object>> deviceInfos = locationDevices.stream()
                                .map(device -> {
                                    Map<String, Object> deviceInfo = new HashMap<>();
                                    deviceInfo.put("id", device.getId());
                                    deviceInfo.put("name", device.getDeviceName());
                                    deviceInfo.put("status", device.getState());
                                    deviceInfo.put("type", device.getDeviceType());
                                    deviceInfo.put("streamKey", device.getStreamKey());
                                    deviceInfo.put("maintainerPhone", device.getMaintainerPhone());
                                    deviceInfo.put("equipmentNumber", device.getEquipmentNumber());
                                    return deviceInfo;
                                })
                                .collect(Collectors.toList());
                        data.put("devices", deviceInfos);
                        // 获取该点位的劝导员信息
                        List<Map<String, Object>> staff = usersMapper.getPersuaders(
                                firstDevice.getCity(),
                                firstDevice.getCounty(),
                                firstDevice.getTownship(),
                                firstDevice.getHamlet(),
                                firstDevice.getSite()
                        );

                        // 获取当天的排班信息
                        LocalDate today = LocalDate.now();
                        List<Schedule> schedules = scheduleMapper.selectSchedulesByDateAndLocation(
                            today,
                            firstDevice.getCity(),
                            firstDevice.getCounty(),
                            firstDevice.getTownship(),
                            firstDevice.getHamlet(),
                            firstDevice.getSite()
                        );

                        // 按用户ID分组排班信息
                        Map<Integer, List<Schedule>> userSchedules = schedules.stream()
                            .collect(Collectors.groupingBy(Schedule::getUserId));

                        // 检查设备在线状态（使用统一的判断方法）
                        boolean isDeviceOnline = isDeviceOnline(locationDevices);

                        // 处理每个staff的状态
                        boolean hasAbnormalStaff = false;  // 添加标记，用于判断是否有异常人员
                        boolean isInScheduleTime = false;  // 添加标记，用于判断是否在排班时间内
                        boolean hasLeaveRecord = false;  // 添加标记，用于判断是否在有人请假
                        // 为每个用户准备按班次组织的异常记录
                        // Map<Integer, Map<String, List<Map<String, Object>>>> userShiftAbnormalRecords = new HashMap<>();
                        
                        // 处理每个用户
                        for (Map<String, Object> staffMember : staff) {
                            Integer userId = (Integer) staffMember.get("user_id");
                            List<Schedule> userScheduleList = userSchedules.getOrDefault(userId, new ArrayList<>());
                            
                            // 按班次分组处理
                            List<Map<String, Object>> shifts = new ArrayList<>();
                            staffMember.put("shifts", shifts);
                            
                            // 处理用户的所有班次
                                for (Schedule schedule : userScheduleList) {
                                    Shift shift = shiftMapper.selectById(schedule.getShiftId());
                                    if (shift != null) {
                                    String shiftName = shift.getStartTime().getHour() < 12 ? "早" : "晚";
                                    
                                    Map<String, Object> shiftInfo = new HashMap<>();
                                    shiftInfo.put("shiftName", shiftName);
                                    shiftInfo.put("startTime", shift.getStartTime().toString());
                                    shiftInfo.put("endTime", shift.getEndTime().toString());
                                    shiftInfo.put("abnormalRecords", new ArrayList<>());
                                    
                                    // 创建地点信息Map
                                    Map<String, String> locationInfo = new HashMap<>();
                                    locationInfo.put("city", firstDevice.getCity());
                                    locationInfo.put("county", firstDevice.getCounty());
                                    locationInfo.put("township", firstDevice.getTownship());
                                    locationInfo.put("hamlet", firstDevice.getHamlet());
                                    locationInfo.put("site", firstDevice.getSite());
                                    
                                    // 时间段
                                        String timeSlot = "早".equals(shiftName) ? "am" : "pm";

                                        // 判断人员状态
                                    List<Map<String, Object>> abnormalRecords = new ArrayList<>();
                                    Map<String, Object> statusResult = determineStaffStatus(
                                        userId, 
                                        Collections.singletonList(schedule), 
                                        isDeviceOnline, 
                                        locationInfo,
                                        timeSlot,
                                        abnormalRecords,
                                            staffMember.get("deptName").toString());
                                    
                                    // 更新班次状态
                                    shiftInfo.put("status", statusResult.get("status"));
                                    
                                    // 更新人员整体状态 - 异常状态优先于正常状态
                                    String currentStaffStatus = (String) staffMember.get("status");
                                    String shiftStatus = (String) statusResult.get("status");
                                    
                                    // 状态优先级：故障 > 脱岗 > 在岗 > 休息
                                    if (currentStaffStatus == null) {
                                        staffMember.put("status", shiftStatus);
                                    } else if ("故障".equals(shiftStatus)) {
                                        // 故障状态优先级最高
                                        staffMember.put("status", "故障");
                                    } else if ("脱岗".equals(shiftStatus) && !"故障".equals(currentStaffStatus)) {
                                        // 脱岗状态仅次于故障
                                        staffMember.put("status", "脱岗");
                                    } else if ("在岗".equals(shiftStatus) && "休息".equals(currentStaffStatus)) {
                                        // 在岗状态优于休息状态
                                        staffMember.put("status", "在岗");
                                    }
                                    // 其他情况保持当前状态不变
                                    
                                    if ((boolean) statusResult.get("isAbnormal")) {
                                        hasAbnormalStaff = true;
                                    }
                                    if ((boolean) statusResult.get("isInScheduleTime")) {
                                        isInScheduleTime = true;
                                    }
                                    
                                    // 添加异常记录
                                    List<Map<String, Object>> shiftAbnormalRecords = new ArrayList<>();
                                    for (Map<String, Object> abnormalRecord : abnormalRecords) {
                                        Map<String, Object> recordInfo = new HashMap<>();
                                        recordInfo.put("type", abnormalRecord.get("type"));
                                        recordInfo.put("startTime", abnormalRecord.get("startTime"));
                                        recordInfo.put("endTime", abnormalRecord.get("endTime"));
                                        recordInfo.put("duration", abnormalRecord.get("duration"));
                                        recordInfo.put("description", abnormalRecord.get("description"));
                                        shiftAbnormalRecords.add(recordInfo);
                                    }
                                    
                                    // 添加异常记录到班次信息中
                                    shiftInfo.put("abnormalRecords", shiftAbnormalRecords);
                                    
                                    // 添加班次信息
                                    shifts.add(shiftInfo);
                                }
                            }
                        }

                        data.put("staff", staff);

                        // 构建排班信息，只包含有排班的人员
                        List<Map<String, Object>> scheduleInfo = new ArrayList<>();
                        for (Map.Entry<Integer, List<Schedule>> entry : userSchedules.entrySet()) {
                            Integer userId = entry.getKey();
                            List<Schedule> userScheduleList = entry.getValue();
                            
                            // 只处理有排班记录的用户
                            if (!userScheduleList.isEmpty()) {
                                Map<String, Object> userSchedule = new HashMap<>();
                                // 获取用户信息
                                Users user = usersMapper.selectById(userId);
                                if (user != null) {
                                    userSchedule.put("userId", userId);
                                    userSchedule.put("userName", user.getName());
                                    
                                    List<Map<String, Object>> shifts = new ArrayList<>();
                                    for (Schedule schedule : userScheduleList) {
                                        Shift shift = shiftMapper.selectById(schedule.getShiftId());
                                        if (shift != null) {
                                            Map<String, Object> shiftInfo = new HashMap<>();
                                            shiftInfo.put("shiftName", shift.getShiftName());
                                            shiftInfo.put("startTime", shift.getStartTime().toString());
                                            shiftInfo.put("endTime", shift.getEndTime().toString());
                                            shifts.add(shiftInfo);
                                        }
                                    }
                                    userSchedule.put("shifts", shifts);
                                    scheduleInfo.add(userSchedule);
                                }
                            }
                        }
                        data.put("schedules", scheduleInfo);
                        // 构建点位唯一标识，使用下划线作为分隔符
                        String locationKey = String.format("%s_%s_%s_%s_%s",
                                firstDevice.getCity(),
                                firstDevice.getCounty(),
                                firstDevice.getTownship(),
                                firstDevice.getHamlet(),
                                firstDevice.getSite());
                        // 获取当前时间判断是上午还是下午
                        String timeSlot = LocalTime.now().getHour() < 12 ? "am" : "pm";
                        // 保存点位状态到Redis，区分上午和下午
                        String redisKey = "site_status:" + locationKey + ":" + timeSlot;
                        // 确定点位状态 - 保持状态判断逻辑不变
                        data.put("status", determineSiteStatus(isDeviceOnline, hasAbnormalStaff, isInScheduleTime, hasLeaveRecord, redisKey));
                        
                        point.put("data", data);
                        return point;
                    } catch (Exception e) {
                        log.error("获取点位数据失败", e);
                        return null;
                    }
                });
                futures.add(future);
            }
            // 收集所有点位数据
            List<Map<String, Object>> points = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(10, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.error("获取点位数据失败", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return SaResult.data(points);
        } catch (Exception e) {
            log.error("获取地图点位详情失败", e);
            return SaResult.error("获取地图点位详情失败: " + e.getMessage());
        }
    }

    // 构建地址字符串
    private String buildAddress(Device device) {
        //StringBuilder address = new StringBuilder(device.getCity());
        StringBuilder address = new StringBuilder();
        if (StringUtils.isNotBlank(device.getCounty())) {
            address.append(device.getCounty());
        }
        if (StringUtils.isNotBlank(device.getTownship())) {
            address.append(device.getTownship());
        }
        if (StringUtils.isNotBlank(device.getHamlet())) {
            address.append(device.getHamlet());
        }
        if (StringUtils.isNotBlank(device.getSite())) {
            address.append(device.getSite());
        }
        return address.toString();
    }

    /**
     * 获取24小时违法趋势统计数据
     * 按小时统计各类型违法行为的数量和劝导情况
     *
     * @param city     城市
     * @param county   区县
     * @param township 镇街道
     * @param hamlet   社区/村
     * @param site     点位
     * @return 统计结果，包含每小时的总体统计和分类详情
     */
    @Override
    public SaResult getHourlyViolationStats(String city, String county, String township,
                                            String hamlet, String site, Date startDate, Date endDate) {
        try {
//            log.info("开始查询24小时违法趋势 - 参数: city={}, county={}, township={}, hamlet={}, site={}, startDate={}, endDate={}",
//                    city, county, township, hamlet, site, startDate, endDate);
            // 解析年月并设置查询时间范围（当月1号到月末）
//            YearMonth ym = YearMonth.parse(yearMonth);
//            LocalDate startDate = ym.atDay(1);
//            LocalDate endDate = ym.atEndOfMonth();
            // 查询原始统计数据
            List<Map<String, Object>> stats = illegalRecordsMapper.getHourlyViolationStats(
                    startDate,
                    endDate,
                    city, county, township, hamlet, site);
            // 初始化24小时的数据结构，每小时包含总计和分类详情
            List<Map<String, Object>> hourlyStats = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Object> hourData = new HashMap<>();
                hourData.put("hour", String.format("%02d:00", hour));  // 格式化小时显示
                hourData.put("totalCount", 0);  // 违法总数
                hourData.put("persuadedCount", 0);  // 劝导成功数
                hourData.put("effectiveCount", 0);  // 有效劝导数
                hourData.put("details", new ArrayList<Map<String, Object>>());  // 各类型详情
                hourlyStats.add(hourData);
            }
            // 用于累计每小时的总计数据
            Map<Integer, Integer> totalByHour = new HashMap<>();  // 每小时违法总数
            Map<Integer, Integer> persuadedByHour = new HashMap<>();  // 每小时劝导成功数
            Map<Integer, Integer> effectiveByHour = new HashMap<>();  // 每小时有效劝导数
            // 定义颜色映射表
            Map<String, String> colorMap = new HashMap<>();
            //colorMap.put("违法总数", "#F56C6C"); // 违法总数 - 红色
            //colorMap.put("劝导率", "#409EFF"); // 劝导率 - 蓝色
            colorMap.put("超员", "#E040FB"); // 超员 - 紫红色
            colorMap.put("未佩戴头盔", "#FF9800"); // 未佩戴头盔 - 橙色
            colorMap.put("加装遮阳伞", "#00BCD4"); // 加装遮阳伞 - 青色
            colorMap.put("超员+未佩戴头盔", "#9C27B0"); // 超员+未佩戴头盔 - 深紫色
            colorMap.put("超员+加装遮阳伞", "#3F51B5"); // 超员+加装遮阳伞 - 靛蓝色
            colorMap.put("未佩戴头盔+加装遮阳伞", "#009688"); // 未佩戴头盔+加装遮阳伞 - 青绿色
            colorMap.put("超员+未佩戴头盔+加装遮阳伞", "#795548"); // 超员+未佩戴头盔+加装遮阳伞 - 棕色
            // 处理查询结果，填充详细数据
            for (Map<String, Object> stat : stats) {
                int hour = ((Number) stat.get("hour")).intValue();
                int type = ((Number) stat.get("type")).intValue();
                int totalCount = ((Number) stat.get("total_count")).intValue();
                int persuadedCount = ((Number) stat.get("persuaded_count")).intValue();
                int effectiveCount = ((Number) stat.get("effective_count")).intValue();

                // 累加每小时的总计数据
                totalByHour.merge(hour, totalCount, Integer::sum);
                persuadedByHour.merge(hour, persuadedCount, Integer::sum);
                effectiveByHour.merge(hour, effectiveCount, Integer::sum);

                // 添加分类详情
                Map<String, Object> hourData = hourlyStats.get(hour);
                List<Map<String, Object>> details = (List<Map<String, Object>>) hourData.get("details");

                // 构建单个类型的统计数据
                Map<String, Object> detail = new HashMap<>();
                String typeDesc = TrafficEnum.of(type).getDesc();  // 违法类型描述
                detail.put("type", typeDesc);
                detail.put("totalCount", totalCount);  // 该类型违法总数
                detail.put("persuadedCount", persuadedCount);  // 该类型劝导成功数
                detail.put("effectiveCount", effectiveCount);  // 该类型有效劝导数
                detail.put("persuasionRate", String.format("%.2f",  // 该类型劝导率
                        totalCount > 0 ? (persuadedCount * 100.0 / totalCount) : 0));
                detail.put("color", colorMap.getOrDefault(typeDesc, "#000000"));  // 该类型颜色

                details.add(detail);
            }

            // 更新每小时的总计数据
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Object> hourData = hourlyStats.get(hour);
                hourData.put("totalCount", totalByHour.getOrDefault(hour, 0));
                hourData.put("persuadedCount", persuadedByHour.getOrDefault(hour, 0));
                hourData.put("effectiveCount", effectiveByHour.getOrDefault(hour, 0));
                int total = totalByHour.getOrDefault(hour, 0);
                int persuaded = persuadedByHour.getOrDefault(hour, 0);
                hourData.put("persuasionRate", String.format("%.2f",  // 总体劝导率
                        total > 0 ? (persuaded * 100.0 / total) : 0));
            }

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate);  // 统计年月
            result.put("endDate", endDate);  // 统计年月
            result.put("hourlyStats", hourlyStats);  // 24小时统计数据

            return SaResult.data(result);
        } catch (Exception e) {
            log.error("查询24小时违法趋势失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }


    @Override
    public SaResult getViolationTypeAnalysisByMonth(String city, String county, String township, String hamlet, String site, Date date) {
        List<Map<String, Object>> statistics = illegalRecordsMapper.getViolationTypeStatisticsByMonth(city, county, township, hamlet, site,date);
        // 转换数据格式
        List<Map<String, Object>> result = statistics.stream()
                .map(stat -> {
                    Map<String, Object> item = new HashMap<>();
                    int typeCode = ((Number) stat.get("type")).intValue();
                    int count = ((Number) stat.get("count")).intValue();
                    TrafficEnum trafficEnum = TrafficEnum.of(typeCode);
                    item.put("name", trafficEnum.getDesc());
                    item.put("value", count);
                    return item;
                })
                .collect(Collectors.toList());

        return SaResult.data(result);
    }

    @Override
    public SaResult getViolationTypeAnalysisByWeek(String city, String county, String township, String hamlet, String site) {
        List<Map<String, Object>> statistics = illegalRecordsMapper.getViolationTypeAnalysisByWeek(city, county, township, hamlet, site);
        // 转换数据格式
        List<Map<String, Object>> result = statistics.stream()
                .map(stat -> {
                    Map<String, Object> item = new HashMap<>();
                    int typeCode = ((Number) stat.get("type")).intValue();
                    int count = ((Number) stat.get("count")).intValue();
                    TrafficEnum trafficEnum = TrafficEnum.of(typeCode);
                    item.put("name", trafficEnum.getDesc());
                    item.put("value", count);
                    return item;
                })
                .collect(Collectors.toList());

        return SaResult.data(result);
    }

    @Override
    public SaResult getViolationTypeAnalysisByYear(String city, String county, String township, String hamlet, String site, String year) {
        List<Map<String, Object>> statistics = illegalRecordsMapper.getViolationTypeAnalysisByYear(city, county, township, hamlet, site,year);
        // 转换数据格式
        List<Map<String, Object>> result = statistics.stream()
                .map(stat -> {
                    Map<String, Object> item = new HashMap<>();
                    int typeCode = ((Number) stat.get("type")).intValue();
                    int count = ((Number) stat.get("count")).intValue();
                    TrafficEnum trafficEnum = TrafficEnum.of(typeCode);
                    item.put("name", trafficEnum.getDesc());
                    item.put("value", count);
                    return item;
                })
                .collect(Collectors.toList());

        return SaResult.data(result);
    }

    @Override
    public SaResult getViolationTypeStatisticsByMap(String city, String county, String township, String hamlet, String site, Date startDate, Date endDate) {
        List<Map<String, Object>> statistics = illegalRecordsMapper.getViolationTypeStatisticsByMap(city, county, township, hamlet, site, startDate, endDate);
        return processPointDetails(statistics);
    }



    // 封装车牌识别的逻辑到单独的方法
    private String recognizePlateNumber(MultiValueMap<String, Object> body, String fallbackPlateNumber) {
        try {
            // 准备请求头和请求体
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            String url = "http://***************:5000/get_plate_number";
            // 设置请求超时
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory());
            ((SimpleClientHttpRequestFactory) restTemplate.getRequestFactory()).setConnectTimeout(10000);
            ((SimpleClientHttpRequestFactory) restTemplate.getRequestFactory()).setReadTimeout(10000);
            String response = restTemplate.postForObject(url, requestEntity, String.class);
            // 解析响应
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response);
            String recognizedPlateNumber = jsonNode.get("plate_number").asText();
//            if (StringUtils.isNotBlank(recognizedPlateNumber)) {
                log.info("车牌识别成功 - 识别结果: {}, 原车牌: {}", recognizedPlateNumber, fallbackPlateNumber);
                return recognizedPlateNumber;
//            }
//            log.info("车牌识别结果为空，使用原车牌号: {}", fallbackPlateNumber);
//            return fallbackPlateNumber;

        } catch (RestClientException e) {
            log.error("车牌识别服务请求失败: {}", e.getMessage());
        } catch (JsonProcessingException e) {
            log.error("车牌识别结果解析失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("车牌识别过程发生未知错误: {}", e.getMessage());
        }
        // 发生任何异常都返回原车牌号
//        log.info("车牌识别失败，使用原车牌号: {}", fallbackPlateNumber);
        return fallbackPlateNumber;
    }

    // 辅助方法：构建地点键值
    private String buildLocationKey(Map<String, Object> stat) {
        return String.format("%s_%s_%s_%s_%s",
                stat.get("city"),
                stat.get("county"),
                stat.get("township"),
                stat.get("hamlet"),
                stat.get("site"));
    }

    // 添加一个新的辅助方法来根据 theNextLevel 构建位置键
    private String buildLocationKeyByLevel(Map<String, Object> stat, String theNextLevel) {
        StringBuilder key = new StringBuilder();
        key.append(stat.get("city"));

        if ("county".equals(theNextLevel) || "township".equals(theNextLevel) ||
                "hamlet".equals(theNextLevel) || "site".equals(theNextLevel)) {
            key.append("-").append(stat.get("county"));
        }

        if ("township".equals(theNextLevel) || "hamlet".equals(theNextLevel) ||
                "site".equals(theNextLevel)) {
            key.append("-").append(stat.get("township"));
        }

        if ("hamlet".equals(theNextLevel) || "site".equals(theNextLevel)) {
            key.append("-").append(stat.get("hamlet"));
        }

        if ("site".equals(theNextLevel)) {
            key.append("-").append(stat.get("site"));
        }

        return key.toString();
    }

    /**
     * 格式化日期时间为简洁格式
     * @param dateTimeStr 日期时间字符串
     * @return 格式化后的日期时间字符串 (yyyy-MM-dd HH:mm:ss)
     */
    private String formatDateTime(String dateTimeStr) {
        try {
            if (dateTimeStr == null || "至今".equals(dateTimeStr)) {
                return "至今";
            }
            
            // 处理不同格式的日期时间字符串
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            // 尝试解析为LocalDateTime
            try {
                LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr);
                return dateTime.format(outputFormatter);
            } catch (Exception e) {
                // 尝试解析为ZonedDateTime (处理包含时区信息的日期)
                try {
                    ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTimeStr);
                    return zonedDateTime.format(outputFormatter);
                } catch (Exception e2) {
                    // 如果仍然无法解析，使用Java默认的Date.toString()格式进行解析
                    // 例如: Thu May 22 07:30:00 CST 2025
                    try {
                        SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
                        Date date = inputFormat.parse(dateTimeStr);
                        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        return outputFormat.format(date);
                    } catch (Exception e3) {
                        // 如果所有解析方法都失败，返回原始字符串
                        return dateTimeStr;
                    }
                }
            }
        } catch (Exception e) {
            return dateTimeStr;
        }
    }

    /**
     * 判断当前时间是否在指定时间范围内
     * @param currentTime 当前时间（HH:mm格式或am/pm）
     * @param startTime 开始时间（HH:mm格式）
     * @param endTime 结束时间（HH:mm格式）
     * @return 是否在时间范围内
     */
    private boolean isTimeInRange(String currentTime, String startTime, String endTime) {
        try {
            // 如果currentTime是am或pm，使用当前实际时间
            if ("am".equals(currentTime) || "pm".equals(currentTime)) {
                currentTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm"));
            }

            // 解析时间
            LocalTime current = LocalTime.parse(currentTime);
            LocalTime start = LocalTime.parse(startTime);
            LocalTime end = LocalTime.parse(endTime);

            // 判断是否在时间范围内
            return !current.isBefore(start) && !current.isAfter(end);
        } catch (Exception e) {
            log.error("时间解析失败: currentTime={}, startTime={}, endTime={}, error={}", 
                currentTime, startTime, endTime, e.getMessage());
            return false;
        }
    }

    /**
     * 检查车牌历史违法记录并自动下派
     * @param illegalRecords 当前违法记录
     * @param plateNumber 车牌号
     */
    private void checkPlateHistoryAndAutoDispatch(IllegalRecords illegalRecords, String plateNumber) {
        try {
            Date now = new Date();

            // 计算近一个月的时间范围
            Date oneMonthAgo = DateUtil.offset(now, DateField.MONTH, -1);
            int monthlyViolations = illegalRecordsMapper.countViolationsByPlateNumber(plateNumber, oneMonthAgo, now);

            // 计算近一年的时间范围
            Date oneYearAgo = DateUtil.offset(now, DateField.YEAR, -1);
            int yearlyViolations = illegalRecordsMapper.countViolationsByPlateNumber(plateNumber, oneYearAgo, now);

            log.info("车牌[{}]历史违法统计：近一个月{}次，近一年{}次", plateNumber, monthlyViolations, yearlyViolations);

            // 判断是否需要自动下派
            boolean shouldDispatch = false;
            String dispatchReason = "";

            if (monthlyViolations >= 3) {
                shouldDispatch = true;
                dispatchReason = String.format("近一个月违法%d次", monthlyViolations);
            } else if (yearlyViolations >= 10) {
                shouldDispatch = true;
                dispatchReason = String.format("近一年违法%d次", yearlyViolations);
            }

            if (shouldDispatch) {
                log.info("车牌[{}]触发自动下派条件：{}", plateNumber, dispatchReason);
                autoDispatchViolation(illegalRecords, dispatchReason);
            }

        } catch (Exception e) {
            log.error("检查车牌[{}]历史违法记录失败: {}", plateNumber, e.getMessage(), e);
        }
    }

    /**
     * 自动下派违法记录
     * @param illegalRecords 违法记录
     * @param reason 下派原因
     */
    private void autoDispatchViolation(IllegalRecords illegalRecords, String reason) {
        try {
            // 查询下派劝导员
            List<Users> users = usersMapper.selectProselytizer(
                    illegalRecords.getCity(),
                    illegalRecords.getCounty(),
                    illegalRecords.getTownship(),
                    illegalRecords.getHamlet(),
                    illegalRecords.getSite()
            );

            if (users.isEmpty()) {
                // 如果没有找到对应点位的劝导员，查找对应村的劝导员
                users = usersMapper.selectProselytizer(
                        illegalRecords.getCity(),
                        illegalRecords.getCounty(),
                        illegalRecords.getTownship(),
                        illegalRecords.getHamlet(),
                        ""
                );
            }

            if (users.isEmpty()) {
                log.info("车牌[{}]自动下派失败：未找到可用的劝导员", illegalRecords.getPlateNumber());
                return;
            }

            // 创建下派记录
            for (Users user : users) {
                AccuratePersuasion accuratePersuasion = new AccuratePersuasion();
                accuratePersuasion.setIllegalName(illegalRecords.getIllegalName());
                accuratePersuasion.setUuid(IdUtil.simpleUUID());
                accuratePersuasion.setIllegalRecordsUuid(illegalRecords.getUuid());
                accuratePersuasion.setUserId(user.getUserId());
                accuratePersuasion.setUserName(user.getName());
                accuratePersuasion.setDisposalStatus(0);

                Date date = new Date();
                Date deadlineDate = DateUtil.offset(date, DateField.DAY_OF_MONTH, 5);
                accuratePersuasion.setDeadlineTime(deadlineDate);
                accuratePersuasion.setLowerFactionType("多次违法自动化下派：" + reason);

                accuratePersuasionMapper.insert(accuratePersuasion);

                log.info("车牌[{}]自动下派给劝导员[{}]，原因：{}",
                        illegalRecords.getPlateNumber(), user.getName(), reason);
            }

            // 更新违法记录状态为已下派
            illegalRecords.setDisposalStatus(SENT_DOWN);

            // 发送通知（如果配置了电话通知）
//            if (relatedconfigurations.getPhone()) {
//                phoneNotifyClient.sendPhoneNotify("收到车牌历史违法自动下派", "15388336114");
//            }

        } catch (Exception e) {
            log.error("车牌[{}]自动下派失败: {}", illegalRecords.getPlateNumber(), e.getMessage(), e);
        }
    }

    /**
     * 删除违法记录及其相关数据
     * @param illegalRecordUuid 违法记录UUID
     */
    private void deleteIllegalRecordWithRelatedData(String illegalRecordUuid) {
        try {
            // 1. 先删除相关的下派记录
            QueryWrapper<AccuratePersuasion> persuasionQuery = new QueryWrapper<>();
            persuasionQuery.eq("illegal_records_uuid", illegalRecordUuid);
            List<AccuratePersuasion> persuasions = accuratePersuasionMapper.selectList(persuasionQuery);

            if (!persuasions.isEmpty()) {
                log.info("删除违法记录[{}]的相关下派记录，共{}条", illegalRecordUuid, persuasions.size());
                for (AccuratePersuasion persuasion : persuasions) {
                    log.info("删除下派记录：劝导员[{}]，下派类型[{}]",
                            persuasion.getUserName(), persuasion.getLowerFactionType());
                }
                accuratePersuasionMapper.delete(persuasionQuery);
            }

            // 2. 删除相关的报警记录（如果有）
            QueryWrapper<Alarm> alarmQuery = new QueryWrapper<>();
            alarmQuery.eq("wf_uuid", illegalRecordUuid);
            List<Alarm> alarms = alarmMapper.selectList(alarmQuery);

            if (!alarms.isEmpty()) {
                log.info("删除违法记录[{}]的相关报警记录，共{}条", illegalRecordUuid, alarms.size());
                alarmMapper.delete(alarmQuery);
            }

            // 3. 最后删除违法记录本身
            int deletedCount = illegalRecordsMapper.deleteById(illegalRecordUuid);
            if (deletedCount > 0) {
                log.info("成功删除违法记录[{}]及其所有相关数据", illegalRecordUuid);
            } else {
                log.info("违法记录[{}]删除失败，记录可能不存在", illegalRecordUuid);
            }

        } catch (Exception e) {
            log.error("删除违法记录[{}]及相关数据失败: {}", illegalRecordUuid, e.getMessage(), e);
            throw new RuntimeException("删除违法记录失败", e);
        }
    }
}

