package com.demo.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.Attendance;
import com.demo.entity.Leave;
import com.demo.entity.Schedule;
import com.demo.entity.Users;
import com.demo.enums.AttendanceEnum;
import com.demo.enums.LeaveStatusEnum;
import com.demo.enums.LeaveTypeEnum;
import com.demo.mapper.AttendanceMapper;
import com.demo.mapper.LeaveMapper;
import com.demo.mapper.ScheduleMapper;
import com.demo.service.LeavePostService;
import com.demo.service.LeaveService;
import com.demo.service.UsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static org.springframework.util.StringUtils.hasText;


/**
 * 请假服务实现类
 */
@Service
public class LeaveServiceImpl extends ServiceImpl<LeaveMapper, Leave> implements LeaveService {

    private static final Logger log = LoggerFactory.getLogger(LeaveServiceImpl.class);

    @Autowired
    private UsersService userService;
    
    @Autowired
    private ScheduleMapper scheduleMapper;
    
    @Autowired
    private AttendanceMapper attendanceMapper;
    
    @Autowired
    private LeavePostService leavePostService;

    @Override
    @Transactional
    public SaResult applyLeave(Integer userId, String userName, Integer leaveType, Date startTime,
                               Date endTime, String reason) {
        log.info("开始申请请假，用户ID: {}, 请假类型: {}, 开始时间: {}, 结束时间: {}", 
                userId, leaveType, startTime, endTime);
        
        // 验证参数
        if (userId == null || leaveType == null || startTime == null || endTime == null || !hasText(reason)) {
            log.info("请假申请参数不完整");
            return SaResult.error("请填写完整的请假信息");
        }
        
        // 验证请假时间
        if (startTime.after(endTime)) {
            log.info("请假开始时间晚于结束时间");
            return SaResult.error("请假开始时间不能晚于结束时间");
        }
        
        if (startTime.before(new Date())) {
            log.info("请假开始时间早于当前时间");
            return SaResult.error("请假开始时间不能早于当前时间");
        }
        
        // 验证请假类型
        if (leaveType < 1 || leaveType > 7) {
            log.info("无效的请假类型: {}", leaveType);
            return SaResult.error("无效的请假类型");
        }
        
        // 检查是否有重叠的请假记录
        LambdaQueryWrapper<Leave> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Leave::getUserId, userId)
                .ne(Leave::getStatus, LeaveStatusEnum.REJECTED.getCode())
                .ne(Leave::getStatus, LeaveStatusEnum.CANCELED.getCode())
                .and(w -> w.between(Leave::getStartTime, startTime, endTime)
                        .or()
                        .between(Leave::getEndTime, startTime, endTime)
                        .or()
                        .nested(n -> n.le(Leave::getStartTime, startTime).ge(Leave::getEndTime, endTime)));
        
        long count = this.count(queryWrapper);
        if (count > 0) {
            log.info("该时间段内已有请假记录");
            return SaResult.error("该时间段内已有请假记录，请调整请假时间");
        }
        
        // 检查请假时间是否与排班时间重叠
        LocalDateTime startDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        
        // 查询该时间段内的排班记录
        List<Schedule> schedules = scheduleMapper.getSchedulesByUserIdAndTimeRange(userId, startDateTime, endDateTime);
        log.info("找到与请假时间重叠的排班记录: {} 条", schedules.size());
        
        if (schedules.isEmpty()) {
            log.info("该时间段内没有排班记录");
            return SaResult.error("该时间段内没有排班记录，无需请假");
        }
        Users user = userService.getById(userId);

        // 创建请假记录
        Leave leave = new Leave();
        leave.setUserId(userId);
        leave.setUserName(userName);
        leave.setLeaveType(leaveType);
        leave.setStartTime(startTime);
        leave.setEndTime(endTime);
        leave.setReason(reason);
        leave.setStatus(LeaveStatusEnum.PENDING.getCode());
        leave.setCreateTime(new Date());
        leave.setUpdateTime(new Date());
        leave.setCity(user.getCity());
        leave.setCounty(user.getCounty());
        leave.setTownship(user.getTownship());
        leave.setHamlet(user.getHamlet());
        leave.setSite(user.getSite());
        
        boolean success = this.save(leave);
        
        if (success) {
            log.info("请假申请提交成功，请假ID: {}", leave.getId());
            return SaResult.ok("请假申请已提交，等待审批");
        } else {
            log.info("请假申请提交失败");
            return SaResult.error("申请失败，请重试");
        }
    }

    @Override
    @Transactional
    public SaResult approveLeave(Integer leaveId, Integer approvalStatus, String approvalComment) {
        log.info("开始处理请假审批，请假ID: {}, 审批状态: {}", leaveId, approvalStatus);
        
        // 验证参数
        if (leaveId == null || approvalStatus == null) {
            log.info("请假审批参数不完整，请假ID: {}, 审批状态: {}", leaveId, approvalStatus);
            return SaResult.error("请提供完整的审批信息");
        }
        
        // 获取请假记录
        Leave leave = this.getById(leaveId);
        if (leave == null) {
            log.info("请假记录不存在，请假ID: {}", leaveId);
            return SaResult.error("请假记录不存在");
        }
        
        // 验证请假状态
        if (!Objects.equals(leave.getStatus(), LeaveStatusEnum.PENDING.getCode())) {
            log.info("请假申请已被处理，无法再次审批，请假ID: {}, 当前状态: {}", leaveId, leave.getStatus());
            return SaResult.error("该请假申请已被处理，无法再次审批");
        }
        
        // 获取当前用户ID
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        log.info("审批人ID: {}", currentUserId);
        
        // 更新请假记录
        leave.setStatus(approvalStatus);
        leave.setApprovedBy(currentUserId);
        leave.setApprovedTime(new Date());
        leave.setApprovalComment(approvalComment);
        leave.setUpdateTime(new Date());
        
        boolean success = this.updateById(leave);
        log.info("更新请假记录状态: {}, 请假ID: {}", success ? "成功" : "失败", leaveId);
        
        // 如果审批通过，更新相应排班的考勤状态
        if (success && approvalStatus.equals(LeaveStatusEnum.APPROVED.getCode())) {
            LocalDateTime startDateTime = leave.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime endDateTime = leave.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime now = LocalDateTime.now();
            
            log.info("请假时间段: {} 至 {}", startDateTime, endDateTime);
            
            // 查询该时间段内的排班记录
            List<Schedule> schedules = scheduleMapper.getSchedulesByUserIdAndTimeRange(leave.getUserId(), startDateTime, endDateTime);
            log.info("找到与请假时间重叠的排班记录: {} 条", schedules.size());
            
            for (Schedule schedule : schedules) {
                log.info("处理排班记录，ID: {}, 日期: {}", schedule.getId(), schedule.getScheduleDate());
                
                // 查询是否已有考勤记录
                Attendance attendance = attendanceMapper.getByScheduleId(schedule.getId());
                
                if (attendance == null) {
                    log.info("排班 {} 没有考勤记录，创建新的请假考勤记录", schedule.getId());
                    // 创建新的考勤记录，标记为请假
                    attendance = new Attendance();
                    attendance.setUserId(leave.getUserId());
                    attendance.setScheduleId(schedule.getId());
                    attendance.setUserName(leave.getUserName());
                    attendance.setStatus(AttendanceEnum.LEAVE);
                    attendance.setCreateTime(LocalDateTime.now());
                    attendance.setUpdateTime(LocalDateTime.now());
                    attendanceMapper.insert(attendance);
                    
                    // 只有当请假开始时间已经到了，才需要检查脱岗记录
                    if (now.isAfter(startDateTime) && now.isBefore(endDateTime)) {
                        log.info("请假已开始，检查并结束排班 {} 的脱岗记录", schedule.getId());
                        // 如果有未结束的脱岗记录，结束它
                        leavePostService.endLeavePostByScheduleId(schedule.getId(), now);
                    }
                } else {
                    log.info("排班 {} 已有考勤记录，更新为请假状态", schedule.getId());
                    // 更新已有考勤记录为请假状态
                    attendance.setStatus(AttendanceEnum.LEAVE);
                    attendance.setUpdateTime(LocalDateTime.now());
                    attendanceMapper.updateById(attendance);
                    
                    // 只有当请假开始时间已经到了，才需要检查脱岗记录
                    if (now.isAfter(startDateTime) && now.isBefore(endDateTime)) {
                        log.info("请假已开始，检查并结束排班 {} 的脱岗记录", schedule.getId());
                        // 如果有未结束的脱岗记录，结束它
                        leavePostService.endLeavePostByScheduleId(schedule.getId(), now);
                    }
                }
            }
            
            log.info("请假审批完成，状态: 已批准，请假ID: {}", leaveId);
            return SaResult.ok("请假申请已批准");
        } else if (success && approvalStatus.equals(LeaveStatusEnum.REJECTED.getCode())) {
            log.info("请假审批完成，状态: 已拒绝，请假ID: {}", leaveId);
            return SaResult.ok("请假申请已拒绝");
        } else {
            log.info("请假审批失败，请假ID: {}", leaveId);
            return SaResult.error("审批失败，请重试");
        }
    }

    @Override
    public SaResult getLeaveRecords(String city, String county, String township, String hamlet, String site,
                                    String userName, Integer status, Date startDate, Date endDate, int page, int size) {
        QueryWrapper<Leave> queryWrapper= new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(city), "city",city );
        queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
        queryWrapper.eq(StringUtils.isNotBlank(township), "township",township );
        queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
        queryWrapper.eq(StringUtils.isNotBlank(site), "site",site );
        queryWrapper.like(StringUtils.isNotBlank(userName), "user_name",userName);
        queryWrapper.eq(status != null, "status", status);
        queryWrapper.between(startDate != null && endDate != null, "start_time", startDate, endDate);
        // 按创建时间降序排序
        queryWrapper.orderByDesc("create_time");
        // 分页查询
        Page<Leave> pageResult = this.page(new Page<>(page, size), queryWrapper);
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotal());
        result.put("pages", pageResult.getPages());
        result.put("current", pageResult.getCurrent());
        result.put("size", pageResult.getSize());
        
        List<Map<String, Object>> records = new ArrayList<>();
        for (Leave leave : pageResult.getRecords()) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", leave.getId());
            record.put("userId", leave.getUserId());
            
            // 获取用户信息
            Users user = userService.getById(leave.getUserId());
            if (user != null) {
                record.put("userName", user.getName());
                record.put("city", user.getCity());
                record.put("county", user.getCounty());
                record.put("township", user.getTownship());
                record.put("hamlet", user.getHamlet());
                record.put("site", user.getSite());
            }
            
            record.put("leaveType", leave.getLeaveType());
            record.put("leaveTypeDesc", LeaveTypeEnum.getDescByCode(leave.getLeaveType()));
            record.put("startTime", leave.getStartTime());
            record.put("endTime", leave.getEndTime());
            
            // 计算请假时长（小时）
            long durationMillis = leave.getEndTime().getTime() - leave.getStartTime().getTime();
            long durationHours = durationMillis / (60 * 60 * 1000);
            record.put("duration", durationHours);
            
            record.put("reason", leave.getReason());
            record.put("status", leave.getStatus());
            record.put("statusDesc", LeaveStatusEnum.getDescByCode(leave.getStatus()));
            record.put("createTime", leave.getCreateTime());
            
            records.add(record);
        }
        
        result.put("records", records);
        
        return SaResult.data(result);
    }

    @Override
    public SaResult getLeaveDetail(Integer leaveId) {
        // 验证参数
        if (leaveId == null) {
            return SaResult.error("请提供请假记录ID");
        }
        
        // 获取请假记录
        Leave leave = this.getById(leaveId);
        if (leave == null) {
            return SaResult.error("请假记录不存在");
        }
        
        // 构建详情信息
        Map<String, Object> detail = new HashMap<>();
        detail.put("id", leave.getId());
        detail.put("userId", leave.getUserId());
        
        // 获取用户信息
        Users user = userService.getById(leave.getUserId());
        if (user != null) {
            detail.put("userName", user.getName());
            detail.put("city", user.getCity());
            detail.put("county", user.getCounty());
            detail.put("township", user.getTownship());
            detail.put("hamlet", user.getHamlet());
            detail.put("site", user.getSite());
        }
        
        detail.put("leaveType", leave.getLeaveType());
        detail.put("leaveTypeDesc", LeaveTypeEnum.getDescByCode(leave.getLeaveType()));
        detail.put("startTime", leave.getStartTime());
        detail.put("endTime", leave.getEndTime());
        
        // 计算请假时长（小时）
        long durationMillis = leave.getEndTime().getTime() - leave.getStartTime().getTime();
        long durationHours = durationMillis / (60 * 60 * 1000);
        detail.put("duration", durationHours);
        
        detail.put("reason", leave.getReason());
        detail.put("status", leave.getStatus());
        detail.put("statusDesc", LeaveStatusEnum.getDescByCode(leave.getStatus()));
        
        // 审批信息
        if (leave.getApprovedBy() != null) {
            detail.put("approvedBy", leave.getApprovedBy());
            Users approver = userService.getById(leave.getApprovedBy());
            if (approver != null) {
                detail.put("approverName", approver.getName());
            }
            detail.put("approvedTime", leave.getApprovedTime());
            detail.put("approvalComment", leave.getApprovalComment());
        }
        
        // 取消信息
        if (leave.getCancelTime() != null) {
            detail.put("cancelReason", leave.getCancelReason());
            detail.put("cancelTime", leave.getCancelTime());
        }
        
        detail.put("createTime", leave.getCreateTime());
        detail.put("updateTime", leave.getUpdateTime());
        
        return SaResult.data(detail);
    }

    @Override
    @Transactional
    public SaResult cancelLeave(Integer leaveId, String reason) {
        log.info("开始取消请假申请，请假ID: {}, 取消原因: {}", leaveId, reason);
        
        // 验证参数
        if (leaveId == null) {
            log.info("取消请假参数不完整，请假ID为空");
            return SaResult.error("请提供请假记录ID");
        }
        
        // 获取请假记录
        Leave leave = this.getById(leaveId);
        if (leave == null) {
            log.info("请假记录不存在，请假ID: {}", leaveId);
            return SaResult.error("请假记录不存在");
        }
        
        // 验证请假状态
        if (!Objects.equals(leave.getStatus(), LeaveStatusEnum.PENDING.getCode()) && leave.getStatus() != LeaveStatusEnum.APPROVED.getCode()) {
            log.info("请假申请已被拒绝或取消，无法再次取消，请假ID: {}, 当前状态: {}", leaveId, leave.getStatus());
            return SaResult.error("该请假申请已被拒绝或取消，无法再次取消");
        }
        
        // 验证请假时间
        if (leave.getStatus() == LeaveStatusEnum.APPROVED.getCode() && leave.getStartTime().before(new Date())) {
            log.info("请假已开始，无法取消，请假ID: {}", leaveId);
            return SaResult.error("请假已开始，无法取消");
        }
        
        // 获取当前用户ID
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        log.info("取消人ID: {}", currentUserId);
        
        // 验证是否为本人或管理员
        if (!currentUserId.equals(leave.getUserId()) && !StpUtil.hasPermission("attendance-management/attendance/leave/admin")) {
            log.info("用户 {} 无权取消用户 {} 的请假申请", currentUserId, leave.getUserId());
            return SaResult.error("您无权取消此请假申请");
        }
        
        // 更新请假记录
        leave.setStatus(LeaveStatusEnum.CANCELED.getCode());
        leave.setCancelReason(reason);
        leave.setCancelTime(new Date());
        leave.setUpdateTime(new Date());
        
        boolean success = this.updateById(leave);
        log.info("更新请假记录状态: {}, 请假ID: {}", success ? "成功" : "失败", leaveId);
        
        // 如果取消成功且之前已批准，需要恢复相应排班的考勤状态
        if (success && leave.getStatus() == LeaveStatusEnum.APPROVED.getCode()) {
            LocalDateTime startDateTime = leave.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime endDateTime = leave.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            
            log.info("请假时间段: {} 至 {}", startDateTime, endDateTime);
            
            // 查询该时间段内的排班记录
            List<Schedule> schedules = scheduleMapper.getSchedulesByUserIdAndTimeRange(leave.getUserId(), startDateTime, endDateTime);
            log.info("找到与请假时间重叠的排班记录: {} 条", schedules.size());
            
            for (Schedule schedule : schedules) {
                log.info("处理排班记录，ID: {}, 日期: {}", schedule.getId(), schedule.getScheduleDate());
                
                // 查询考勤记录
                Attendance attendance = attendanceMapper.getByScheduleId(schedule.getId());
                
                if (attendance != null && attendance.getStatus() == AttendanceEnum.LEAVE) {
                    log.info("删除排班 {} 的请假考勤记录", schedule.getId());
                    // 删除请假状态的考勤记录
                    attendanceMapper.deleteById(attendance.getId());
                } else {
                    log.info("排班 {} 没有请假考勤记录或状态不是请假", schedule.getId());
                }
            }
        }
        
        if (success) {
            log.info("请假取消完成，请假ID: {}", leaveId);
            return SaResult.ok("请假申请已取消");
        } else {
            log.info("请假取消失败，请假ID: {}", leaveId);
            return SaResult.error("取消失败，请重试");
        }
    }
} 